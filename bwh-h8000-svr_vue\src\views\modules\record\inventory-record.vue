<template>
  <div class="mod-config">
    <el-form :inline="true"
             :model="dataForm"
             @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptKey"
                   placeholder="请选择科室"
                   clearable
                   @click.native="getDeptList">
          <el-option v-for="item in deptList"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.deviceKey"
                   placeholder="请选择设备"
                   clearable
                   @click.native="getDeviceList">
          <el-option v-for="item in deviceList"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.key"
                  placeholder="请输入盘点单号"
                  clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.operatorKey"
                  placeholder="请输入操作人员名称或工号"
                  clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.epc"
                  placeholder="请输入耗材EPC"
                  clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="timeRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        @change="handleTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList"
              border
              v-loading="dataListLoading"
              style="width: 100%;">
      <el-table-column type="index"
                       header-align="center"
                       align="center"
                       width="50"
                       label="序号">
      </el-table-column>
      <el-table-column prop="pdOrder"
                       header-align="center"
                       align="center"
                       label="盘点单号">
      </el-table-column>
      <el-table-column prop="deptName"
                       header-align="center"
                       align="center"
                       label="科室">
      </el-table-column>
      <el-table-column prop="deviceName"
                       header-align="center"
                       align="center"
                       label="机柜名称">
      </el-table-column>
      <el-table-column prop="deviceDetailName"
                       header-align="center"
                       align="center"
                       label="子柜名称">
      </el-table-column>
      <el-table-column prop="operaName"
                       header-align="center"
                       align="center"
                       label="操作人">
      </el-table-column>
      <el-table-column prop="operaJobNo"
                       header-align="center"
                       align="center"
                       label="工号">
      </el-table-column>
      <el-table-column prop="creatTime"
                       header-align="center"
                       align="center"
                       label="创建时间">
      </el-table-column>
      <el-table-column prop="biType"
                       header-align="center"
                       align="center"
                       label="验证类型">
        <template slot-scope="scope">
          {{ getBiTypeText(scope.row.biType) }}
        </template>
      </el-table-column>
      <el-table-column prop="pdType"
                       header-align="center"
                       align="center"
                       label="盘点模式">
        <template slot-scope="scope">
          {{ getPdTypeText(scope.row.pdType) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right"
                       header-align="center"
                       align="center"
                       width="100"
                       label="操作">
        <template slot-scope="scope">
          <el-button type="text"
                     size="small"
                     @click="showDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle"
                   @current-change="currentChangeHandle"
                   :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]"
                   :page-size="pageSize"
                   :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 记录详情弹窗 -->
    <el-dialog title="记录详情"
               :visible.sync="detailVisible"
               width="80%"
               :close-on-click-modal="false">
      <inventory-record-detail v-if="detailVisible"
                               :record-id="currentRecordId"></inventory-record-detail>
    </el-dialog>
  </div>
</template>

<script>
import InventoryRecordDetail from './inventory-record-detail.vue'

export default {
  components: {
    InventoryRecordDetail
  },
  data () {
    return {
      dataForm: {
        key: '',
        operatorKey: '',
        startTime: '',
        endTime: '',
        deviceKey: '',
        deptKey: '',
        epc: ''
      },
      timeRange: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      deptList: [],
      deviceList: [],
      detailVisible: false,
      currentRecordId: 0
    }
  },
  activated () {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList (resetPage = false) {
      this.dataListLoading = true
      if (resetPage) {
        this.pageIndex = 1
      }
      this.$http({
        url: this.$http.adornUrl('/inventoryRecord/info'),
        method: 'post',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'operatorKey': this.dataForm.operatorKey,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime,
          'deviceKey': this.dataForm.deviceKey,
          'deptKey': this.dataForm.deptKey,
          'epc': this.dataForm.epc,
          'page': this.pageIndex,
          'limit': this.pageSize
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.data.list
          this.totalPage = data.data.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取科室列表
    getDeptList () {
      this.$http({
        url: this.$http.adornUrl('/basic/dept/info'),
        method: 'post',
        data: this.$http.adornData({
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.deptList = data.data.list
        } else {
          this.deptList = []
        }
      })
    },
    // 获取设备列表
    getDeviceList () {
      this.$http({
        url: this.$http.adornUrl('/device/info'),
        method: 'post',
        data: this.$http.adornData({
          'deptKey': this.dataForm.deptKey,
          'page': 1,
          'limit': -1
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.deviceList = data.data.list
        } else {
          this.deviceList = []
        }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 处理时间范围变化
    handleTimeChange (val) {
      if (val) {
        this.dataForm.startTime = val[0]
        this.dataForm.endTime = val[1]
      } else {
        this.dataForm.startTime = ''
        this.dataForm.endTime = ''
      }
    },
    // 显示详情
    showDetail (row) {
      this.currentRecordId = row.id
      this.detailVisible = true
    },
    // 获取生物识别类型文本
    getBiTypeText (type) {
      const typeMap = {
        1: 'IC卡',
        2: '指静脉',
        3: '人脸',
        4: '机械',
        5: '应急码',
        6: '密码',
        7: '指纹'
      }
      return typeMap[type] || '未知类型'
    },
    // 获取盘点模式文本
    getPdTypeText (type) {
      const typeMap = {
        0: '自动',
        1: '手动'
      }
      return typeMap[type] || '未知模式'
    }
  }
}
</script> 