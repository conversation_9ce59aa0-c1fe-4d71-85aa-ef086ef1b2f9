<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptId" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.resName" placeholder="请输入耗材名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.resEpc" placeholder="请输入耗材唯一码" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.resBatchNo" placeholder="请输入批号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.userJobNo" placeholder="请输入取用人工号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.userName" placeholder="请输入取用人姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="resName" header-align="center" align="center" label="物品名称">
        <template slot-scope="scope">
          {{ scope.row.resName ? scope.row.resName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resEpc" header-align="center" align="center" label="标签号">
        <template slot-scope="scope">
          {{ scope.row.resEpc ? scope.row.resEpc : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resUnit" header-align="center" align="center" label="规格">
        <template slot-scope="scope">
          {{ scope.row.resUnit ? scope.row.resUnit : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="supplierName" header-align="center" align="center" label="供应商名称">
        <template slot-scope="scope">
          {{ scope.row.supplierName ? scope.row.supplierName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resBatchNo" header-align="center" align="center" label="批号">
        <template slot-scope="scope">
          {{ scope.row.resBatchNo ? scope.row.resBatchNo : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="科室名称">
        <template slot-scope="scope">
          {{ scope.row.deptName ? scope.row.deptName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deptCode" header-align="center" align="center" label="科室代码">
        <template slot-scope="scope">
          {{ scope.row.deptCode ? scope.row.deptCode : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="userName" header-align="center" align="center" label="取用人姓名">
        <template slot-scope="scope">
          {{ scope.row.userName ? scope.row.userName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="userJobNo" header-align="center" align="center" label="取用人工号">
        <template slot-scope="scope">
          {{ scope.row.userJobNo ? scope.row.userJobNo : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" header-align="center" align="center" label="最后操作时间">
        <template slot-scope="scope">
          {{ scope.row.updateTime ? scope.row.updateTime : '-' }}
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          resName: '',
          resEpc: '',
          resBatchNo: '',
          deptId: '',
          supplierName: '',
          cabCode: '',
          userJobNo: '',
          userName: '',
          page: 1,
          limit: 10
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        deptList: [],
        cirStatusList: [
          { value: 0, label: '取用未消耗', type: 'warning' },
          { value: 1, label: '已消耗', type: 'success' },
          { value: 2, label: '已退回', type: 'info' }
        ]
      }
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/infoSelect/unconsumed/list'),
          method: 'post',
          data: this.$http.adornData({
            'resName': this.dataForm.resName,
            'resEpc': this.dataForm.resEpc,
            'resBatchNo': this.dataForm.resBatchNo,
            'deptId': this.dataForm.deptId,
            'supplierName': this.dataForm.supplierName,
            'cabCode': this.dataForm.cabCode,
            'userJobNo': this.dataForm.userJobNo,
            'userName': this.dataForm.userName,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deptList = data.data.list
          } else {
            this.deptList = []
          }
        })
      },
      // 获取流通状态文本
      getCirStatusText (type) {
        const item = this.cirStatusList.find(item => item.value === type)
        return item ? item.label : '未知'
      },
      // 获取流通状态标签样式
      getCirStatusType (type) {
        const item = this.cirStatusList.find(item => item.value === type)
        return item ? item.type : 'info'
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script>
