<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="所属医院" prop="hospitalId">
        <el-select v-model="dataForm.hospitalId" placeholder="请选择医院" style="width: 100%">
          <el-option
            v-for="item in hospitalList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="科室编码" prop="code">
        <el-input v-model="dataForm.code" placeholder="请输入科室编码"></el-input>
      </el-form-item>
      <el-form-item label="科室名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="请输入科室名称"></el-input>
      </el-form-item>
      <el-form-item label="位置" prop="address">
        <el-input v-model="dataForm.address" placeholder="请输入科室位置"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        hospitalList: [],
        dataForm: {
          id: 0,
          code: '',
          name: '',
          address: '',
          hospitalId: ''
        },
        dataRule: {
          code: [
            { required: true, message: '科室编码不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '科室名称不能为空', trigger: 'blur' }
          ],
          hospitalId: [
            { required: true, message: '请选择医院', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      // 获取医院列表
      getHospitalList () {
        this.$http({
          url: this.$http.adornUrl('/basic/hospital/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({ data }) => {
          if (data.data && data.code === 0) {
            this.hospitalList = data.data.list
          }
        })
      },
      init (row) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.getHospitalList()
          if (row) {
            this.dataForm = {
              ...this.dataForm,
              ...row
            }
          } else {
            this.dataForm = {
              id: 0,
              code: '',
              name: '',
              address: '',
              hospitalId: ''
            }
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/basic/dept/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
