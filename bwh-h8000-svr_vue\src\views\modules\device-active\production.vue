<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getActiveKey()">
      <el-form-item>
        <el-input v-model="dataForm.deviceKey" placeholder="请输入设备序列号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dataForm.expireDate"
          type="date"
          placeholder="选择到期时间"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getActiveKey">生成激活码</el-button>
      </el-form-item>
    </el-form>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>激活码信息</span>
      </div>
      <div class="text item" v-if="activeKey">
        <p>设备序列号：{{ dataForm.deviceKey }}</p>
        <p>到期时间：{{ dataForm.expireDate }}</p>
        <p>激活码：{{ activeKey }}</p>
      </div>
      <div class="text item empty-tip" v-else>
        <i class="el-icon-info"></i>
        <span>请先生成激活码</span>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        deviceKey: '',
        expireDate: ''
      },
      activeKey: '',
      pickerOptions: {
        disabledDate (time) {
          // 只能选择今天之后的日期
          return time.getTime() < Date.now() - 8.64e7
        }
      }
    }
  },
  watch: {
    'dataForm.deviceKey' (val) {
      if (!val) {
        this.activeKey = ''
      }
    },
    'dataForm.expireDate' (val) {
      if (!val) {
        this.activeKey = ''
      }
    }
  },
  methods: {
    // 生成激活码
    getActiveKey () {
      if (!this.dataForm.deviceKey) {
        this.$message.error('请输入设备序列号')
        return
      }
      if (!this.dataForm.expireDate) {
        this.$message.error('请选择到期时间')
        return
      }
      // 将日期转换为年月日格式
      const date = new Date(this.dataForm.expireDate)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const activeDayTime = parseInt(`${year}${month}${day}`)
      this.$http({
        url: this.$http.adornUrl('/device/getActiveKey'),
        method: 'post',
        data: this.$http.adornData({
          activeDayTime,
          deviceKey: this.dataForm.deviceKey
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.activeKey = data.activeKey
          this.$message.success('激活码生成成功')
        } else {
          this.$message.error(data.msg || '生成失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-top: 20px;
  width: 100%;
}
.text {
  font-size: 14px;
}
.item {
  margin-bottom: 18px;
}
.empty-tip {
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}
.empty-tip i {
  margin-right: 5px;
  font-size: 14px;
}
</style>
