<style>
  .el-checkbox.checkbox {
    margin-left: 0;
    margin-right: 30px;
  }

  .dialog-box1>div {
    width: 50%;
    padding: 20px 20px 10px;
  }
</style>
<template>
  <el-dialog class="dialog-box1" :close-on-click-modal="false" :visible.sync="visible" @close="handleClose">
    <h4 v-show="name">{{ `${name} - ${appId}` }}</h4>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="80px">
      <el-form-item label="版本名称" prop="versionName">
        <el-input v-model="dataForm.versionName" placeholder="版本名称" @input="
            e => {
              dataForm.appName = validForbid(e, 50);
            }
          "></el-input>
      </el-form-item>
      <el-form-item label="版本号" prop="versionCode">
        <el-input v-model="dataForm.versionCode" placeholder="版本号"></el-input>
      </el-form-item>
      <el-form-item label="版本信息" prop="versionInfo">
        <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" v-model="dataForm.versionInfo"
          placeholder="版本信息"></el-input>
      </el-form-item>
      <el-upload class="upload-demo" ref="upload" :action="url" :on-success="successHandle" :multiple="false"
        :data="dataForm" style="display: block;margin-left: 40px" :auto-upload="false">
        <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
      </el-upload>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitUpload">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        url: '',
        name: '',
        appId: '',
        visible: false,
        dataForm: {
          appInfoId: '',
          versionName: '',
          versionCode: '',
          versionInfo: ''
        },
        dataRule: {
          versionName: [
            { required: true, message: '版本名称不能为空', trigger: 'blur' }
          ],
          versionCode: [
            { required: true, message: '版本号不能为空', trigger: 'blur' }
          ],
          versionInfo: [
            { required: true, message: '版本信息不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id, name, appId) {
        this.name = name
        this.appId = appId
        this.dataForm.appInfoId = id
        this.visible = true
        // TODO 上传文件url
        this.url = this.$http.adornUrl(
          `/ota/appinfo/uploadAppVersion?token=${this.$cookie.get('token')}`
        )
      },
      submitUpload () {
        // 如果没有选择文件，直接返回
        if (this.$refs.upload.uploadFiles.length === 0) {
          this.$message({
            message: '请选择上传文件!',
            type: 'warning'
          })
          return
        }
        this.$refs['dataForm'].validate(valid => {
          if (valid) {
            this.$refs.upload.submit()
          }
        })
      },
      successHandle (response, file) {
        if (response && response.code === 0) {
          this.dataForm.hospitalId = null
          this.$message({
            message: response.msg,
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(response.msg)
        }
      },
      handleClose () {
        this.dataForm = {
          appInfoId: '',
          versionName: '',
          versionCode: '',
          versionInfo: ''
        }
        this.$refs.upload.clearFiles()
        this.$emit('close')
      }
    }
  }
</script>