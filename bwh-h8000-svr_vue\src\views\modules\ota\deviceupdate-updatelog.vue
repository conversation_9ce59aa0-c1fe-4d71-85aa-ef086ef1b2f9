<template>
  <el-dialog title="更新日志" :visible.sync="visible" center>
    <el-form :inline="true" :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="search()">
      <el-form-item>
        <el-input v-model="dataForm.deviceName" placeholder="请输入设备名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="appInfoId">
        <el-select filterable clearable @change="handleChange" v-model="dataForm.appInfoId" placeholder="请选择升级软件">
          <el-option v-for="item in appInfoList" :label="item.name" :key="item.index" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="updateVersionId">
        <el-select filterable clearable multiple collapse-tags :disabled="!dataForm.appInfoId"
          v-model="dataForm.updateVersionId" placeholder="请选择软件版本" @click.native="getVersionList">
          <el-option v-for="item in versionList" :label="item.name" :key="item.index" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="search">查 询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" class="customer-no-border-table" v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="deviceName" header-align="center" align="center" label="名称"></el-table-column>
      <el-table-column prop="deviceCode" header-align="center" align="center" label="编码">
      </el-table-column>
      <el-table-column prop="appName" header-align="center" align="center" label="升级软件">
      </el-table-column>
      <el-table-column prop="versionName" header-align="center" align="center" label="目标版本">
      </el-table-column>
      <el-table-column prop="updateState" header-align="center" align="center" width="100" label="更新状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.updateState === 0">未更新</el-tag>
          <el-tag v-else-if="scope.row.updateState === 1">正在验证</el-tag>
          <el-tag v-else-if="scope.row.updateState === 2">下载中</el-tag>
          <el-tag v-else-if="scope.row.updateState === 3">待安装</el-tag>
          <el-tag v-else-if="scope.row.updateState === 4">安装中</el-tag>
          <el-tag v-else-if="scope.row.updateState === 5" type="success">安装完成</el-tag>
          <el-tag v-else-if="scope.row.updateState === 6" type="info">用户取消</el-tag>
          <el-tag v-else-if="scope.row.updateState === 7" type="danger">下载失败</el-tag>
          <el-tag v-else-if="scope.row.updateState === 8" type="danger">安装失败</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" header-align="center" align="center" label="上报时间">
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      var checkUpdateVersion = (rule, value, callback) => {
        const { appInfoId } = this.dataForm
        if (appInfoId) {
          if (!value || value.length === 0) {
            callback(new Error('请选择软件版本'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
      return {
        dataForm: {
          deviceName: '',
          appInfoId: '',
          updateVersionId: []
        },
        dataRule: {
          updateVersionId: [{ validator: checkUpdateVersion, trigger: 'blur' }]
        },
        appInfoList: [],
        versionList: [],
        visible: false,
        dataListLoading: false,
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        id: 0,
        name: ''
      }
    },
    methods: {
      init () {
        this.visible = true
        this.$nextTick(() => {
          this.$http({
            // TODO 获取应用列表url
            url: this.$http.adornUrl(`/ota/appinfo/getAppSelect`),
            method: 'post'
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.appInfoList = data.list
            } else {
              this.$message.error(data.msg)
            }
            this.search()
          })
        })
      },
      search () {
        this.pageIndex = 1
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.$refs['dataForm'].validate(valid => {
          if (valid) {
            this.dataListLoading = true
            this.$http({
              // TODO 获取升级日志url
              url: this.$http.adornUrl('/ota/device/log'),
              method: 'post',
              data: this.$http.adornParams({
                page: this.pageIndex,
                limit: this.pageSize,
                deviceName: this.dataForm.deviceName,
                versionIds: this.dataForm.updateVersionId
                  ? this.dataForm.updateVersionId
                  : []
              })
            })
              .then(({ data }) => {
                if (data && data.code === 0) {
                  if (data.page.list.length === 0) {
                    this.pageIndex = 1
                    if (data.page.totalCount > 0) {
                      this.getDataList()
                    } else {
                      this.dataList = []
                      this.totalPage = 0
                    }
                  } else {
                    this.dataList = data.page.list
                    this.totalPage = data.page.totalCount
                  }
                } else {
                  this.dataList = []
                  this.totalPage = 0
                }
              })
              .finally(() => {
                this.dataListLoading = false
              })
          }
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      getVersionList () {
        this.$http({
          // TODO 获取应用版本url
          url: this.$http.adornUrl('/ota/appinfo/getAppVersionSelect'),
          method: 'post',
          params: this.$http.adornParams({
            appInfoId: this.dataForm.appInfoId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.versionList = data.list
          } else {
            this.versionList = []
          }
        })
      },
      handleChange () {
        this.dataForm.updateVersionId = ''
      }
    }
  }
</script>

<style>
  .el-descriptions-label {
    width: 200px;
    background-color: #e6e2e2;
    color: #909399;
    padding-left: 20px;
  }
</style>