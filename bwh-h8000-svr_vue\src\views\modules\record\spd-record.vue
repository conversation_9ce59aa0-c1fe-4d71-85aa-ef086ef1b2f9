<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptKey" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="请输入订单号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.operatorKey" placeholder="请输入操作人员名称或工号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.epc" placeholder="请输入耗材EPC" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.type" placeholder="请选择订单类型" clearable>
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="spdOrder" header-align="center" align="center" label="业务单号">
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="操作科室">
      </el-table-column>
      <el-table-column prop="operaName" header-align="center" align="center" label="操作人">
      </el-table-column>
      <el-table-column prop="operaJobno" header-align="center" align="center" label="工号">
      </el-table-column>
      <el-table-column prop="opTime" header-align="center" align="center" label="操作时间">
      </el-table-column>
      <el-table-column prop="opType" header-align="center" align="center" label="操作类型">
        <template slot-scope="scope">
          {{ getOpTypeText(scope.row.opType) }}
        </template>
      </el-table-column>
      <el-table-column prop="countNum" header-align="center" align="center" label="操作总数">
      </el-table-column>
      <el-table-column prop="orderStatus" header-align="center" align="center" label="订单状态">
        <template slot-scope="scope">
          {{ getOrderStatusText(scope.row.orderStatus) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="100" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="showDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 记录详情弹窗 -->
    <el-dialog title="记录详情" :visible.sync="detailVisible" width="80%" :close-on-click-modal="false">
      <spd-record-detail v-if="detailVisible" :record-id="currentRecordId"></spd-record-detail>
    </el-dialog>
  </div>
</template>

<script>
  import SpdRecordDetail from './spd-record-detail.vue'

  export default {
    components: {
      SpdRecordDetail
    },
    data () {
      return {
        dataForm: {
          key: '',
          operatorKey: '',
          startTime: '',
          endTime: '',
          deptKey: '',
          epc: '',
          type: ''
        },
        timeRange: [],
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        deptList: [],
        detailVisible: false,
        currentRecordId: 0,
        typeList: [
          { value: 0, label: '取出' },
          { value: 1, label: '存入' },
          { value: 2, label: '入库' },
          { value: 3, label: '退货' },
          { value: 4, label: '未入库' },
          { value: 5, label: '待定' },
          { value: 6, label: '驳回退货' },
          { value: 7, label: '消耗' },
          { value: 8, label: '取消消耗' },
          { value: 9, label: '物品下架' },
          { value: 10, label: '更新供应商信息' },
          { value: 11, label: '手工消耗' }
        ]
      }
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/spdRecord/info'),
          method: 'post',
          data: this.$http.adornData({
            'key': this.dataForm.key,
            'operatorKey': this.dataForm.operatorKey,
            'startTime': this.dataForm.startTime,
            'endTime': this.dataForm.endTime,
            'deptKey': this.dataForm.deptKey,
            'epc': this.dataForm.epc,
            'type': this.dataForm.type,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deptList = data.data.list
          } else {
            this.deptList = []
          }
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 处理时间范围变化
      handleTimeChange (val) {
        if (val) {
          this.dataForm.startTime = val[0]
          this.dataForm.endTime = val[1]
        } else {
          this.dataForm.startTime = ''
          this.dataForm.endTime = ''
        }
      },
      // 显示详情
      showDetail (row) {
        this.currentRecordId = row.id
        this.detailVisible = true
      },
      // 获取操作类型文本
      getOpTypeText (type) {
        const typeMap = {
          0: '取出',
          1: '存入',
          2: '入库',
          3: '退货',
          4: '未入库',
          5: '待定',
          6: '驳回退货',
          7: '消耗',
          8: '取消消耗',
          9: '物品下架',
          10: '更新供应商信息',
          11: '手工消耗'
        }
        return typeMap[type] || '未知类型'
      },
      // 获取订单状态文本
      getOrderStatusText (status) {
        const statusMap = {
          0: '刚创建',
          1: '部分完成',
          2: '全部完成'
        }
        return statusMap[status] || '未知状态'
      }
    }
  }
</script>
