<template>
  <div class="mod-config">
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="resName" header-align="center" align="center" label="物品名称">
        <template slot-scope="scope">
          {{ scope.row.resName || '未知物品' }}
        </template>
      </el-table-column>
      <el-table-column prop="resEpc" header-align="center" align="center" label="耗材唯一码">
      </el-table-column>
      <el-table-column prop="resUnit" header-align="center" align="center" label="规格">
      </el-table-column>
      <el-table-column prop="resDw" header-align="center" align="center" label="单位">
      </el-table-column>
      <el-table-column prop="resTrans" header-align="center" align="center" label="包装系数">
      </el-table-column>
      <el-table-column prop="resBatchNo" header-align="center" align="center" label="批号">
      </el-table-column>
      <el-table-column prop="produceDate" header-align="center" align="center" label="生产日期">
      </el-table-column>
      <el-table-column prop="expiredDate" header-align="center" align="center" label="有效日期">
      </el-table-column>
      <el-table-column prop="opType" header-align="center" align="center" label="操作类型">
        <template slot-scope="scope">
          {{ getOpTypeText(scope.row.opType) }}
        </template>
      </el-table-column>
      <el-table-column prop="operaJobno" header-align="center" align="center" label="工号">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    props: {
      recordId: {
        type: [Number, String],
        default: 0
      }
    },
    data () {
      return {
        dataList: [],
        dataListLoading: false
      }
    },
    watch: {
      recordId: {
        handler (val) {
          if (val) {
            this.getDataList()
          }
        },
        immediate: true
      }
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/spdRecord/detailInfo'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.recordId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data
          } else {
            this.dataList = []
          }
          this.dataListLoading = false
        })
      },
      // 获取操作类型文本
      getOpTypeText (type) {
        const typeMap = {
          0: '取出',
          1: '存入',
          2: '入库',
          3: '退货',
          4: '未入库',
          5: '待定',
          6: '驳回退货',
          7: '消耗',
          8: '取消消耗',
          9: '物品下架',
          10: '更新供应商信息',
          11: '手工消耗'
        }
        return typeMap[type] || '未知类型'
      }
    }
  }
</script>
