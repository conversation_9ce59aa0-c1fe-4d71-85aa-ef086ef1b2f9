<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptId" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.deviceId" placeholder="请选择设备" clearable @click.native="getDeviceList">
          <el-option v-for="item in deviceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.deviceDetailId" placeholder="请选择子柜" clearable @click.native="getDeviceDetailList">
          <el-option v-for="item in deviceDetailList" :key="item.id" :label="item.deviceDetailCode" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.resEpc" placeholder="请输入物品唯一码" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="deviceName" header-align="center" align="center" label="设备名称">
      </el-table-column>
      <el-table-column prop="deviceCode" header-align="center" align="center" label="设备编码">
      </el-table-column>
      <el-table-column prop="deviceAddress" header-align="center" align="center" label="设备地址">
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="所属科室">
      </el-table-column>
      <el-table-column prop="deviceDetailCode" header-align="center" align="center" label="子柜编号">
      </el-table-column>
      <el-table-column prop="stockCount" header-align="center" align="center" label="库存数量">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="100" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="showDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 库存详情弹窗 -->
    <el-dialog title="库存详情" :visible.sync="detailVisible" width="80%" :close-on-click-modal="false">
      <stock-info-detail v-if="detailVisible" 
        :device-id="currentDeviceId"
        :device-detail-id="currentDeviceDetailId"
        :dept-id="dataForm.deptId">
      </stock-info-detail>
    </el-dialog>
  </div>
</template>

<script>
  import StockInfoDetail from './stock-info-detail.vue'

  export default {
    components: {
      StockInfoDetail
    },
    data () {
      return {
        dataForm: {
          deptId: '',
          deviceId: '',
          deviceDetailId: '',
          resEpc: '',
          page: 1,
          limit: 10
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        deptList: [],
        deviceList: [],
        deviceDetailList: [],
        detailVisible: false,
        currentDeviceId: 0,
        currentDeviceDetailId: 0
      }
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/infoSelect/stockQuery/queryStockInfo'),
          method: 'post',
          data: this.$http.adornData({
            'deptId': this.dataForm.deptId,
            'deviceId': this.dataForm.deviceId,
            'deviceDetailId': this.dataForm.deviceDetailId,
            'resEpc': this.dataForm.resEpc,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deptList = data.data.list
          } else {
            this.deptList = []
          }
        })
      },
      // 获取设备列表
      getDeviceList () {
        this.$http({
          url: this.$http.adornUrl('/device/info'),
          method: 'post',
          data: this.$http.adornData({
            'deptKey': this.dataForm.deptId,
            'page': 1,
            'limit': 1000
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deviceList = data.data.list
            this.getDeviceDetailList()
          } else {
            this.deviceList = []
            this.deviceDetailList = []
          }
        })
      },
      // 获取子柜列表
      getDeviceDetailList () {
        // if (!this.dataForm.deviceId) {
        //   this.deviceDetailList = []
        //   return
        // }
        this.$http({
          url: this.$http.adornUrl('/device/getDeviceDetails'),
          method: 'post',
          data: this.$http.adornData({
            'deviceId': this.dataForm.deviceId,
            'page': 1,
            'limit': -1
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deviceDetailList = data.data.list
          } else {
            this.deviceDetailList = []
          }
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 显示详情
      showDetail (row) {
        this.currentDeviceId = row.deviceId
        this.currentDeviceDetailId = row.deviceDetailId
        this.detailVisible = true
      }
    },
    watch: {
      'dataForm.deptId' () {
        this.dataForm.deviceId = ''
        this.dataForm.deviceDetailId = ''
        this.deviceList = []
        this.deviceDetailList = []
      },
      'dataForm.deviceId' () {
        this.dataForm.deviceDetailId = ''
        this.getDeviceDetailList()
      }
    }
  }
</script>
