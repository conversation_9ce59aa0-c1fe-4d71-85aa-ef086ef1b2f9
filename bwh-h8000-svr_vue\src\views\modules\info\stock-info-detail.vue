<template>
  <div class="mod-config">
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="resName" header-align="center" align="center" label="物品名称">
      </el-table-column>
      <el-table-column prop="resUnit" header-align="center" align="center" label="规格">
      </el-table-column>
      <el-table-column prop="resEpc" header-align="center" align="center" label="物品标签唯一码">
      </el-table-column>
      <el-table-column prop="expiredDate" header-align="center" align="center" label="物品截至日期">
      </el-table-column>
      <el-table-column prop="resBatchNo" header-align="center" align="center" label="批号">
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    props: {
      deviceId: {
        type: [Number, String],
        default: 0
      },
      deviceDetailId: {
        type: [Number, String],
        default: 0
      },
      deptId: {
        type: [Number, String],
        default: 0
      }
    },
    data () {
      return {
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false
      }
    },
    watch: {
      deviceDetailId: {
        handler (val) {
          if (val) {
            this.getDataList()
          }
        },
        immediate: true
      }
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/infoSelect/stockQuery/queryStockDetail'),
          method: 'post',
          data: this.$http.adornData({
            'deviceId': this.deviceId,
            'deviceDetailId': this.deviceDetailId,
            'deptId': this.deptId,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script> 