<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.resEpc" placeholder="请输入耗材唯一码" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="resName" header-align="center" align="center" label="物品名称">
        <template slot-scope="scope">
          {{ scope.row.resName ? scope.row.resName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resUnit" header-align="center" align="center" label="规格">
        <template slot-scope="scope">
          {{ scope.row.resUnit ? scope.row.resUnit : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resEpc" header-align="center" align="center" label="物品标签唯一码">
      </el-table-column>
      <el-table-column prop="expiredDate" header-align="center" align="center" label="近效期/截止日期">
        <template slot-scope="scope">
          {{ scope.row.expiredDate ? scope.row.expiredDate : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resBatchNo" header-align="center" align="center" label="批号">
        <template slot-scope="scope">
          {{ scope.row.resBatchNo ? scope.row.resBatchNo : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="supName" header-align="center" align="center" label="供应商名称">
        <template slot-scope="scope">
          {{ scope.row.supName ? scope.row.supName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="opTime" header-align="center" align="center" label="操作时间">
        <template slot-scope="scope">
          {{ scope.row.opTime ? scope.row.opTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="operaName" header-align="center" align="center" label="操作人">
        <template slot-scope="scope">
          {{ scope.row.operaName ? scope.row.operaName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="operaJobno" header-align="center" align="center" label="工号">
        <template slot-scope="scope">
          {{ scope.row.operaJobno ? scope.row.operaJobno : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceCode" header-align="center" align="center" label="操作设备编码">
        <template slot-scope="scope">
          {{ scope.row.deviceCode ? scope.row.deviceCode : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceDetailCode" header-align="center" align="center" label="操作子柜编码">
        <template slot-scope="scope">
          {{ scope.row.deviceDetailCode ? scope.row.deviceDetailCode : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="cirStatus" header-align="center" align="center" label="操作类型">
        <template slot-scope="scope">
          <el-tag :type="getCirStatusType(scope.row.cirStatus)">
            {{ getCirStatusText(scope.row.cirStatus) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          resEpc: '',
          page: 1,
          limit: 10
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        cirStatusList: [
          { value: 0, label: '取出', type: 'info' },
          { value: 1, label: '存入', type: 'success' },
          { value: 2, label: '入库', type: 'success' },
          { value: 3, label: '退货', type: 'warning' },
          { value: 4, label: '未入库', type: 'warning' },
          { value: 5, label: '待定', type: 'info' },
          { value: 6, label: '驳回退货', type: 'danger' },
          { value: 7, label: '消耗', type: 'danger' },
          { value: 8, label: '取消消耗', type: 'info' },
          { value: 9, label: '物品下架', type: 'warning' },
          { value: 10, label: '更新供应商信息', type: 'info' },
          { value: 11, label: '手工消耗', type: 'danger' }
        ]
      }
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        if (!this.dataForm.resEpc) {
          this.$message.warning('请输入耗材唯一码')
          return
        }
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/infoSelect/resourceTrace/query'),
          method: 'post',
          data: this.$http.adornData({
            'resEpc': this.dataForm.resEpc,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取操作类型文本
      getCirStatusText (type) {
        const item = this.cirStatusList.find(item => item.value === type)
        return item ? item.label : '未知'
      },
      // 获取操作类型标签样式
      getCirStatusType (type) {
        const item = this.cirStatusList.find(item => item.value === type)
        return item ? item.type : 'info'
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script>
