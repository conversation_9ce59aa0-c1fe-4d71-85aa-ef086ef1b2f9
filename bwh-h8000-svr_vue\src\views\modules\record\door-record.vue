<template>
  <div class="mod-config">
    <el-form :inline="true"
             :model="dataForm"
             @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deviceKey"
                   placeholder="请选择设备"
                   clearable
                   @click.native="getDeviceList">
          <el-option v-for="item in deviceList"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.operatorKey"
                  placeholder="请输入操作人员名称或工号"
                  clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.type"
                   placeholder="请选择开门类型"
                   clearable>
          <el-option v-for="item in openTypeOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="timeRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        @change="handleTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList"
              border
              v-loading="dataListLoading"
              style="width: 100%;">
      <el-table-column type="index"
                       header-align="center"
                       align="center"
                       width="50"
                       label="序号">
      </el-table-column>
            <el-table-column prop="deviceName"
                       header-align="center"
                       align="center"
                       label="设备名称">
      </el-table-column>
      <el-table-column prop="userName"
                       header-align="center"
                       align="center"
                       label="用户名">
      </el-table-column>
      <el-table-column prop="jobNo"
                       header-align="center"
                       align="center"
                       label="工号">
      </el-table-column>
      <el-table-column prop="operaTime"
                       header-align="center"
                       align="center"
                       label="操作时间">
      </el-table-column>
      <el-table-column prop="closeTime"
                       header-align="center"
                       align="center"
                       label="关闭时间">
      </el-table-column>
      <el-table-column prop="biType"
                       header-align="center"
                       align="center"
                       label="生物识别类型">
        <template slot-scope="scope">
          {{ getBiTypeText(scope.row.biType) }}
        </template>
      </el-table-column>
      <el-table-column prop="openType"
                       header-align="center"
                       align="center"
                       label="开门类型">
        <template slot-scope="scope">
          {{ getOpenTypeText(scope.row.openType) }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle"
                   @current-change="currentChangeHandle"
                   :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]"
                   :page-size="pageSize"
                   :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
export default {
  components: {
  },
  data () {
    return {
      dataForm: {
        operatorKey: '',
        startTime: '',
        endTime: '',
        deviceKey: '',
        type: null
      },
      timeRange: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      deviceList: [],
      detailVisible: false,
      currentRecordId: 0,
      openTypeOptions: [
        { value: 1, label: '正常开门' },
        { value: 2, label: '应急开门' },
        { value: 3, label: '异常开门' }
      ]
    }
  },
  activated () {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList (resetPage = false) {
      this.dataListLoading = true
      if (resetPage) {
        this.pageIndex = 1
      }
      this.$http({
        url: this.$http.adornUrl('/doorRecord/info'),
        method: 'post',
        data: this.$http.adornData({
          'operatorKey': this.dataForm.operatorKey,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime,
          'deviceKey': this.dataForm.deviceKey,
          'type': this.dataForm.type,
          'page': this.pageIndex,
          'limit': this.pageSize
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.data.list
          this.totalPage = data.data.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取设备列表
    getDeviceList () {
      this.$http({
        url: this.$http.adornUrl('/device/info'),
        method: 'post',
        data: this.$http.adornData({
          'page': 1,
          'limit': -1
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.deviceList = data.data.list
        } else {
          this.deviceList = []
        }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 处理时间范围变化
    handleTimeChange (val) {
      if (val) {
        this.dataForm.startTime = val[0]
        this.dataForm.endTime = val[1]
      } else {
        this.dataForm.startTime = ''
        this.dataForm.endTime = ''
      }
    },
    // 显示详情
    showDetail (row) {
      this.currentRecordId = row.id
      this.detailVisible = true
    },
    // 获取生物识别类型文本
    getBiTypeText (type) {
      const typeMap = {
        1: 'IC卡',
        2: '指静脉',
        3: '人脸',
        4: '机械',
        5: '应急码',
        6: '密码',
        7: '指纹'
      }
      return typeMap[type] || '未知类型'
    },
    // 获取开门类型文本
    getOpenTypeText (type) {
      const typeMap = {
        1: '正常开门',
        2: '异常开门',
        3: '强制开门'
      }
      return typeMap[type] || '未知类型'
    }
  }
}
</script> 