<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input-number v-model="dataForm.expireDays" :min="0" placeholder="请输入到期天数" clearable></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="resName" header-align="center" align="center" label="物品名称">
        <template slot-scope="scope">
          {{ scope.row.resName ? scope.row.resName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resEpc" header-align="center" align="center" label="唯一码">
        <template slot-scope="scope">
          {{ scope.row.resEpc ? scope.row.resEpc : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resUnit" header-align="center" align="center" label="规格">
        <template slot-scope="scope">
          {{ scope.row.resUnit ? scope.row.resUnit : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="supName" header-align="center" align="center" label="供应商名称">
        <template slot-scope="scope">
          {{ scope.row.supName ? scope.row.supName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resBatchNo" header-align="center" align="center" label="批号">
        <template slot-scope="scope">
          {{ scope.row.resBatchNo ? scope.row.resBatchNo : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="isInsideDesc" header-align="center" align="center" label="是否在柜内">
        <template slot-scope="scope">
          {{ scope.row.isInsideDesc ? scope.row.isInsideDesc : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="cabCode" header-align="center" align="center" label="存放机柜编码">
        <template slot-scope="scope">
          {{ scope.row.cabCode != '未知' ? scope.row.cabCode : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="科室名称">
        <template slot-scope="scope">
          {{ scope.row.deptName ? scope.row.deptName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deptCode" header-align="center" align="center" label="科室代码">
        <template slot-scope="scope">
          {{ scope.row.deptCode ? scope.row.deptCode : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="cirStatusDesc" header-align="center" align="center" label="流转状态">
        <template slot-scope="scope">
          {{ scope.row.cirStatusDesc ? scope.row.cirStatusDesc : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="updateCirStatusTime" header-align="center" align="center" label="最后更新时间">
        <template slot-scope="scope">
          {{ scope.row.updateCirStatusTime ? scope.row.updateCirStatusTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="expiredDate" header-align="center" align="center" label="到期日期">
        <template slot-scope="scope">
          {{ scope.row.expiredDate ? scope.row.expiredDate : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="remainDays" header-align="center" align="center" label="剩余/过期天数">
        <template slot-scope="scope">
          <el-tag :type="getRemainDaysType(scope.row.remainDays)">
            {{ getRemainDaysText(scope.row.remainDays) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          expireDays: 0,
          page: 1,
          limit: 10
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false
      }
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        if (this.dataForm.expireDays === null || this.dataForm.expireDays === '') {
          this.$message.warning('请输入到期天数')
          return
        }
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/infoSelect/expireDate/query'),
          method: 'post',
          data: this.$http.adornData({
            'expireDays': this.dataForm.expireDays,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取剩余天数标签样式
      getRemainDaysType (days) {
        if (days < 0) {
          return 'danger'
        } else if (days <= 7) {
          return 'warning'
        } else {
          return 'success'
        }
      },
      // 获取剩余天数文本
      getRemainDaysText (days) {
        if (days < 0) {
          return `已过期${Math.abs(days)}天`
        } else if (days === 0) {
          return '今天到期'
        } else {
          return `剩余${days}天`
        }
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script>
