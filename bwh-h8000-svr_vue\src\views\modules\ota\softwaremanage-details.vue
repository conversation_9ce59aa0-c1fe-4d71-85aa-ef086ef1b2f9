<template>
  <el-dialog title="历史版本" :visible.sync="visible" center>
    <h4 v-show="name">{{ `${name} - ${appId}` }}</h4>
    <el-table :data="dataList" class="customer-no-border-table" v-loading="dataListLoading" style="width: 100%;">
      <el-table-column label="序号" header-align="center" align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="versionName" header-align="center" align="center" label="版本名称">
      </el-table-column>
      <el-table-column prop="versionCode" header-align="center" align="center" label="版本号">
      </el-table-column>
      <el-table-column prop="versionInfo" header-align="center" align="center" label="版本信息">
      </el-table-column>
      <el-table-column prop="createTime" header-align="center" width="200" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="uploadUserName" header-align="center" align="center" label="创建人">
      </el-table-column>
      <el-table-column prop="url" header-align="center" align="center" label="操作">
        <template slot-scope="scope">
          <el-link :href="scope.row.url" target="_blank">下载</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </el-dialog>
</template>

<script>
  import { Link } from 'element-ui'

  export default {
    name: 'SoftwareManageDetails',
    components: {
      'el-link': Link
    },
    data () {
      return {
        visible: false,
        dataListLoading: false,
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        id: 0,
        name: '',
        appId: '',
        dataForm: {
          id: 0,
          name: '',
          type: '',
          version: '',
          status: '',
          createTime: '',
          updateTime: '',
          description: ''
        }
      }
    },
    methods: {
      init (id, name, appId) {
        this.visible = true
        this.id = id
        this.name = name
        this.appId = appId
        this.getDataList(id)
      },
      // 获取数据列表
      getDataList (id) {
        this.dataListLoading = true
        this.$http({
          // TODO 获取软件详情url
          url: this.$http.adornUrl('/ota/appinfo/getAppVersionHistory'),
          method: 'post',
          data: this.$http.adornParams({
            page: this.pageIndex,
            limit: this.pageSize,
            appInfoId: id
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              if (data.page.list.length === 0) {
                this.pageIndex = 1
                if (data.page.totalCount > 0) {
                  this.getDataList()
                } else {
                  this.dataList = []
                  this.totalPage = 0
                }
              } else {
                this.dataList = data.page.list
                this.totalPage = data.page.totalCount
              }
            } else {
              this.dataList = []
              this.totalPage = 0
            }
          })
          .finally(() => {
            this.dataListLoading = false
          })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList(this.id)
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList(this.id)
      }
      // init (id) {
      //   this.dataForm.id = id || 0
      //   this.visible = true
      //   this.$nextTick(() => {
      //     if (this.dataForm.id) {
      //       this.$http({
      //         url: this.$http.adornUrl(`/sys/software/info/${this.dataForm.id}`),
      //         method: 'get',
      //         params: this.$http.adornParams()
      //       }).then(({ data }) => {
      //         if (data && data.code === 0) {
      //           this.dataForm.name = data.software.name
      //           this.dataForm.type = data.software.type
      //           this.dataForm.version = data.software.version
      //           this.dataForm.status = data.software.status
      //           this.dataForm.createTime = data.software.createTime
      //           this.dataForm.updateTime = data.software.updateTime
      //           this.dataForm.description = data.software.description
      //         }
      //       })
      //     }
      //   })
      // }
    }
  }
</script>

<style>
  .el-descriptions-label {
    width: 200px;
    background-color: #e6e2e2;
    color: #909399;
    padding-left: 20px;
  }
</style>