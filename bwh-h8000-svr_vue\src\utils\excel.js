import * as XLSX from 'xlsx'

/**
 * 导出科室取用报表
 * @param {Array} dataList 数据列表
 * @param {Array} dateColumns 日期列
 * @param {Object} deptList 科室列表
 * @param {Object} dataForm 表单数据
 * @param {Array} typeList 类型列表
 */
export const exportDeptPickReport = (dataList, dateColumns, deptList, dataForm, typeList) => {
  if (!dataList.length) {
    return
  }

  // 获取当前科室名称
  const currentDept = deptList.find(item => item.id === dataForm.deptKey)
  const deptName = currentDept ? currentDept.name : '未知科室'

  // 获取当前操作类型名称
  const currentType = typeList.find(item => item.value === dataForm.type)
  const typeName = currentType ? currentType.label : '未知类型'

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  // 准备数据
  const headers = ['物品名称', ...dateColumns.map(date => date.split('-')[1] + '-' + date.split('-')[2]), '合计']
  const data = [
    [`${deptName} - ${typeName}报表`],
    [`时间范围：${dataForm.startTime} 至 ${dataForm.endTime}`],
    [],
    headers
  ]

  // 添加数据行
  dataList.forEach(row => {
    const rowData = [row.resName || '未知物品']
    dateColumns.forEach(date => {
      const dayData = row.listByDay.find(item => item.day === date)
      rowData.push(dayData ? dayData.count : 0)
    })
    rowData.push(row.listByDay.reduce((total, item) => total + item.count, 0))
    data.push(rowData)
  })

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(data)

  // 设置列宽
  const colWidths = headers.map(() => ({ wch: 15 }))
  ws['!cols'] = colWidths

  // 合并标题行
  ws['!merges'] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: headers.length - 1 } }
  ]

  // 将工作表添加到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '科室' + typeName + '取用报表')

  // 导出文件
  const fileName = `${deptName}_${typeName}_${dataForm.startTime.split(' ')[0]}_${dataForm.endTime.split(' ')[0]}.xlsx`
  XLSX.writeFile(wb, fileName)
}
