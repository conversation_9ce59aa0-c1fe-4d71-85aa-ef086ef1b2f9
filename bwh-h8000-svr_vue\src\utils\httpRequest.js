import Vue from 'vue'
import axios from 'axios'
import router from '@/router'
import qs from 'qs'
import merge from 'lodash/merge'
import { clearLoginInfo } from '@/utils'

const http = axios.create({
  timeout: 1000 * 30,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json; charset=utf-8'
  }
})

/**
 * 请求拦截
 */
http.interceptors.request.use(config => {
  config.headers['token'] = Vue.cookie.get('token') // 请求头带上token
  return config
}, error => {
  return Promise.reject(error)
})

// /**
//  * 响应拦截
//  */
// http.interceptors.response.use(response => {
//   if (response.data && response.data.code === 401) { // 401, token失效
//     clearLoginInfo()
//     router.push({ name: 'login' })
//   }
//   return response
// }, error => {
//   return Promise.reject(error)
// })
/**
 * 请求拦截
 */
http.interceptors.request.use(config => {
  config.headers['token'] = Vue.cookie.get('token') // 请求头带上token
  return config
}, error => {
  return Promise.reject(error)
})
// /**
//  * 响应拦截
//  */
// http.interceptors.response.use(response => {
//   if (response.data && response.data.code === 401) { // 401, token失效
//     clearLoginInfo()
//     router.push({ name: 'login' })
//   }
//   return response
// }, error => {
//   return Promise.reject(error)
// })
/**
 * 响应拦截（修复版）
 */
http.interceptors.response.use(response => {
  console.log(response)
  // 保留原有的401处理
  if (response.data && response.data.code === 401) {
    clearLoginInfo()
    router.push({ name: 'login' })
  }
  return response
}, error => {
  // 统一错误处理逻辑（增强健壮性）
  var errorMessage = '请求异常'
  // 网络错误（服务器无响应、跨域、DNS解析失败等）
  if (!error.response) {
    if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请检查网络连接'
    } else if (error.message.includes('Network Error')) {
      errorMessage = '网络已断开，请检查网络设置'
    } else if (error.message.includes('ERR_ADDRESS_UNREACHABLE')) {
      errorMessage = '服务器地址不可达，请联系管理员'
    }
  } else {
    const status = error.response.status
    switch (status) {
      case 401:
        clearLoginInfo()
        router.push({ name: 'login' })
        return Promise.reject(error) // 不再显示额外提示
      case 403:
        errorMessage = '没有操作权限'
        break
      case 404:
        errorMessage = '请求资源不存在'
        break
      case 500:
        errorMessage = '服务器内部错误'
        break
      default:
        errorMessage = `服务器错误: ${status}`
    }
  }
  // 使用Vue全局方法显示错误提示，避免this指向问题
  Vue.prototype.$message && Vue.prototype.$message.error(errorMessage)
  return Promise.reject(error)
})
/**
 * 请求地址处理
 * @param {*} actionName action方法名称
 */
http.adornUrl = (actionName) => {
  // 非生产环境 && 开启代理, 接口前缀统一使用[/proxyApi/]前缀做代理拦截!
  return (process.env.NODE_ENV !== 'production' && process.env.OPEN_PROXY ? '/proxyApi/' : window.SITE_CONFIG.baseUrl) + actionName
}

/**
 * get请求参数处理
 * @param {*} params 参数对象
 * @param {*} openDefaultParams 是否开启默认参数?
 */
http.adornParams = (params = {}, openDefaultParams = true) => {
  var defaults = {
    't': new Date().getTime()
  }
  return openDefaultParams ? merge(defaults, params) : params
}

/**
 * post请求数据处理
 * @param {*} data 数据对象
 * @param {*} openDefaultData 是否开启默认数据?
 * @param {*} contentType 数据格式
 *  json: 'application/json; charset=utf-8'
 *  form: 'application/x-www-form-urlencoded; charset=utf-8'
 */
http.adornData = (data = {}, openDefaultData = true, contentType = 'json') => {
  var defaults = {
    't': new Date().getTime()
  }
  data = openDefaultData ? merge(defaults, data) : data
  return contentType === 'json' ? JSON.stringify(data) : qs.stringify(data)
}

export default http
