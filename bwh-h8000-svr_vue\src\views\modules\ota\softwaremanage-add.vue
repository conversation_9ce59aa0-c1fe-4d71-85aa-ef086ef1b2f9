<template>
  <el-dialog
    title="新增"
    :close-on-click-modal="false"
    :visible.sync="visible"
    @close="handleClose"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit"
      label-width="80px"
    >
      <el-form-item label="软件名称" prop="appName">
        <el-input
          v-model="dataForm.appName"
          placeholder="软件名称"
          @input="
            e => {
              dataForm.appName = validForbid(e, 50);
            }
          "
        ></el-input>
      </el-form-item>
      <el-form-item label="软件ID" prop="appInfoId">
        <el-input v-model="dataForm.appInfoId" placeholder="软件ID"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        appName: '',
        appInfoId: ''
      },
      factoryList: [],
      dataRule: {
        appName: [
          { required: true, message: '软件名称不能为空', trigger: 'blur' }
        ],
        appInfoId: [
          { required: true, message: '软件ID不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.dataForm.appName = ''
      this.dataForm.appInfoId = ''
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            // TODO 新增软件信息url
            url: this.$http.adornUrl('/ota/appinfo/add'),
            method: 'post',
            data: this.$http.adornData({
              appName: this.dataForm.appName,
              appId: this.dataForm.appInfoId
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    handleClose () {
      this.$emit('close')
    }
  }
}
</script>
