<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="物品名称" prop="resName">
        <el-input v-model="dataForm.resName" placeholder="请输入物品名称"></el-input>
      </el-form-item>
      <el-form-item label="拼音" prop="resPy">
        <el-input v-model="dataForm.resPy" placeholder="请输入拼音"></el-input>
      </el-form-item>
      <el-form-item label="规格" prop="resUnit">
        <el-input v-model="dataForm.resUnit" placeholder="请输入规格"></el-input>
      </el-form-item>
      <el-form-item label="单位" prop="resDw">
        <el-input v-model="dataForm.resDw" placeholder="请输入单位"></el-input>
      </el-form-item>
      <el-form-item label="产地" prop="resPlace">
        <el-input v-model="dataForm.resPlace" placeholder="请输入产地"></el-input>
      </el-form-item>
      <el-form-item label="包装系数" prop="resTrans">
        <el-input-number v-model="dataForm.resTrans" :min="0" :max="999"></el-input-number>
      </el-form-item>
      <el-form-item label="供应商" prop="supName">
        <el-input v-model="dataForm.supName" placeholder="请输入供应商名称"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="resMemo">
        <el-input v-model="dataForm.resMemo" placeholder="请输入备注"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          resName: '',
          resPy: '',
          resUnit: '',
          resDw: '',
          resPlace: '',
          resMemo: '',
          resTrans: 0,
          supName: ''
        },
        dataRule: {
          resName: [
            { required: true, message: '物品名称不能为空', trigger: 'blur' }
          ],
          resPy: [
            { required: true, message: '拼音不能为空', trigger: 'blur' }
          ],
          resUnit: [
            { required: true, message: '规格不能为空', trigger: 'blur' }
          ],
          resDw: [
            { required: true, message: '单位不能为空', trigger: 'blur' }
          ],
          resPlace: [
            { required: true, message: '产地不能为空', trigger: 'blur' }
          ],
          resTrans: [
            { required: true, message: '包装系数不能为空', trigger: 'blur' }
          ],
          supName: [
            { required: true, message: '供应商不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (row) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (row) {
            this.dataForm = {
              ...this.dataForm,
              ...row
            }
          } else {
            this.dataForm = {
              id: 0,
              resName: '',
              resPy: '',
              resUnit: '',
              resDw: '',
              resPlace: '',
              resMemo: '',
              resTrans: 0,
              supName: ''
            }
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/consumable/resBasicDic/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
