<template>
  <div class="mod-config">
    <div class="info-container">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>设备基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="基本信息">
            <div class="network-info">
              <div class="info-item">
                <span class="label">设备名称：</span>
                <span class="value">{{ deviceInfo.name }}</span>
              </div>
              <div class="info-item">
                <span class="label">设备编号：</span>
                <span class="value">{{ deviceInfo.code }}</span>
              </div>
              <div class="info-item">
                <span class="label">设备位置：</span>
                <span class="value">{{ deviceInfo.address }}</span>
              </div>
              <div class="info-item">
                <span class="label">所属医院：</span>
                <span class="value">{{ deviceInfo.hospitalName }}</span>
              </div>
              <div class="info-item">
                <span class="label">所属科室：</span>
                <span class="value">{{ deviceInfo.deptName }}</span>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="设备信息">
            <div class="network-info">
              <div class="info-item">
                <span class="label">设备序列号：</span>
                <span class="value">{{ deviceInfo.serialNum }}</span>
              </div>
              <div class="info-item">
                <span class="label">子柜数量：</span>
                <span class="value">{{ deviceInfo.detailNum }}</span>
              </div>
              <div class="info-item">
                <span class="label">到期时间：</span>
                <span class="value">{{ deviceInfo.expirationTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">人脸识别：</span>
                <span class="value">
                  <el-tag :type="deviceInfo.arcFaceActiveState === 1 ? 'success' : 'info'">
                    {{ deviceInfo.arcFaceActiveState === 1 ? '已激活' : '未激活' }}
                  </el-tag>
                </span>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="时间信息">
            <div class="network-info">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ deviceInfo.createTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">更新时间：</span>
                <span class="value">{{ deviceInfo.updateTime }}</span>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>网络配置信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="内网配置">
            <div class="network-info">
              <div class="info-item">
                <span class="label">内网IP地址：</span>
                <span class="value">{{ deviceInfo.lanIp }}</span>
              </div>
              <div class="info-item">
                <span class="label">内网网关：</span>
                <span class="value">{{ deviceInfo.lanGateway }}</span>
              </div>
              <div class="info-item">
                <span class="label">内网子网掩码：</span>
                <span class="value">{{ deviceInfo.lanSubmask }}</span>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="外网配置">
            <div class="network-info">
              <div class="info-item">
                <span class="label">外网IP地址：</span>
                <span class="value">{{ deviceInfo.ip }}</span>
              </div>
              <div class="info-item">
                <span class="label">外网网关：</span>
                <span class="value">{{ deviceInfo.gateway }}</span>
              </div>
              <div class="info-item">
                <span class="label">外网子网掩码：</span>
                <span class="value">{{ deviceInfo.submask }}</span>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="其他配置">
            <div class="network-info">
              <div class="info-item">
                <span class="label">录像机IP：</span>
                <span class="value">{{ deviceInfo.videoIp }}</span>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>子柜信息</span>
      </div>
      <el-table :data="detailList" border v-loading="dataListLoading" style="width: 100%;">
        <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
        </el-table-column>
        <el-table-column prop="deviceDetailName" header-align="center" align="center" label="子柜名称">
        </el-table-column>
        <el-table-column prop="deviceDetailCode" header-align="center" align="center" label="柜号">
        </el-table-column>
        <el-table-column prop="isMain" header-align="center" align="center" label="柜类型">
          <template slot-scope="scope">
            {{ scope.row.isMain === 1 ? '主柜' : '副柜' }}
          </template>
        </el-table-column>
        <el-table-column prop="readerIp" header-align="center" align="center" label="读写器IP">
        </el-table-column>
        <el-table-column prop="controlMac" header-align="center" align="center" label="控制板MAC">
        </el-table-column>
        <el-table-column prop="controlIp" header-align="center" align="center" label="控制板IP">
        </el-table-column>
        <el-table-column prop="faceMachineIp" header-align="center" align="center" label="人脸机IP">
        </el-table-column>
        <el-table-column prop="isUse" header-align="center" align="center" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isUse === 1 ? 'success' : 'info'">
              {{ scope.row.isUse === 1 ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" header-align="center" align="center" label="创建时间">
        </el-table-column>
        <el-table-column prop="updateTime" header-align="center" align="center" label="更新时间">
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
  export default {
    props: {
      deviceInfo: {
        type: Object,
        default: () => ({})
      },
      deviceId: {
        type: [Number, String],
        default: 0
      }
    },
    data () {
      return {
        detailList: [],
        dataListLoading: false
      }
    },
    watch: {
      deviceId: {
        handler (val) {
          if (val) {
            this.getDetailList()
          }
        },
        immediate: true
      }
    },
    methods: {
      // 获取子柜列表
      getDetailList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/device/detailInfo'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.deviceId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.detailList = data.data
          } else {
            this.detailList = []
          }
          this.dataListLoading = false
        })
      }
    }
  }
</script>

<style scoped>
  .info-container {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }

  .info-container .box-card {
    flex: 1;
    margin-bottom: 0;
  }

  .el-descriptions {
    margin-bottom: 20px;
  }

  .network-info {
    padding: 5px 0;
  }

  .info-item {
    margin-bottom: 8px;
  }

  .info-item:last-child {
    margin-bottom: 0;
  }

  .label {
    color: #606266;
    margin-right: 8px;
  }

  .value {
    color: #303133;
  }
</style>