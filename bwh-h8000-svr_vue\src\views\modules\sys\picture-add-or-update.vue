<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="医院选择" prop="hospitalId">
      <el-select v-model="dataForm.hospitalId" placeholder="请选择医院" style="width: 100%">
        <el-option
          v-for="item in hospitalList"
          :key="item.id"
          :label="item.hospitalName"
          :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="图片类型" prop="type">
      <el-select v-model="dataForm.type" placeholder="请选择图片类型" style="width: 100%">
        <el-option label="屏保" :value="1"></el-option>
        <el-option label="ICON" :value="2"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="图片名称" prop="pictureName">
      <el-input :disabled = "true" v-model="dataForm.pictureName" placeholder="请上传图片，自动生成"></el-input>
    </el-form-item>
    <el-form-item label="假日名称" prop="holidayName">
      <el-input v-model="dataForm.holidayName" placeholder="假日名称"></el-input>
    </el-form-item>
    <el-form-item label="开始时间" prop="holidayBegtime">
      <el-date-picker v-model="dataForm.pickTime" type="daterange" unlink-panels range-separator=""
          value-format="yyyy-MM-dd" start-placeholder="节日开始时间" end-placeholder="节日结束时间">
        </el-date-picker>
    </el-form-item>
    <el-form-item label="图片地址" prop="pictureUrl">
      <el-upload
      class="avatar-uploader"
      action="http://127.0.0.1:9010/CFMS/sys/picture/savePicture/"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload">
      <i class="el-icon-plus avatar-uploader-icon"></i>
  </el-upload>
      <!-- <el-input v-model="dataForm.pictureUrl" placeholder="图片地址"></el-input> -->
    </el-form-item>
    <div>
      <img style=" width: 200px;
      height: 100px;" v-if="imageUrl" :src="imageUrl" class="avatar">
    </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        hospitalList: [], // 医院列表
        dataForm: {
          id: 0,
          hospitalId: '', // 医院ID
          pictureUrl: '',
          type: 1,
          pictureName: '',
          holidayName: '',
          holidayBegtime: '',
          holidayEndtime: '',
          operatroId: '',
          createTime: '',
          pickTime: null,
          updateTime: ''
        },
        imageUrl: '',
        dataRule: {
          hospitalId: [
            { required: true, message: '请选择医院', trigger: 'change' }
          ],
          type: [
            { required: true, message: '请选择图片类型', trigger: 'change' }
          ],
          pictureUrl: [
            { required: true, message: '图片地址不能为空', trigger: 'blur' }
          ],
          pictureName: [
            { required: true, message: '图片名称不能为空', trigger: 'blur' }
          ],
          holidayName: [
            { required: true, message: '假日名称不能为空', trigger: 'blur' }
          ],
          operatroId: [
            { required: true, message: '操作人iD不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        console.log(11111)
        this.dataForm.id = id || 0
        this.visible = true
        this.imageUrl = ''
        // 获取医院列表
        this.getHospitalList()
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/sys/picture/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              console.log(data)
              if (data && data.code === 0) {
                console.log(data)
                this.dataForm.hospitalId = data.sysPicture.hospitalId
                this.dataForm.pictureUrl = data.sysPicture.pictureUrl
                this.dataForm.pictureName = data.sysPicture.pictureName
                this.dataForm.holidayName = data.sysPicture.holidayName
                this.dataForm.holidayBegtime = data.sysPicture.holidayBegtime
                this.dataForm.holidayEndtime = data.sysPicture.holidayEndtime
                this.dataForm.pickTime = [data.sysPicture.holidayEndtime, data.sysPicture.holidayEndtime]
                this.dataForm.operatroId = data.sysPicture.operatroId
                this.dataForm.createTime = data.sysPicture.createTime
                this.dataForm.updateTime = data.sysPicture.updateTime
                this.dataForm.type = data.sysPicture.type
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/sys/picture/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'hospitalId': this.dataForm.hospitalId,
                'type': this.dataForm.type,
                'pictureUrl': this.dataForm.pictureUrl,
                'pictureName': this.dataForm.pictureName,
                'holidayName': this.dataForm.holidayName,
                'holidayBegtime': this.dataForm.pickTime === null ? '' : this.dataForm.pickTime[0] + ' 00:00:00',
                'holidayEndtime': this.dataForm.pickTime === null ? '' : this.dataForm.pickTime[1] + ' 23:59:59',
                'operatroId': this.dataForm.operatroId,
                'createTime': this.dataForm.createTime,
                'updateTime': this.dataForm.updateTime
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      handleAvatarSuccess (data, file) {
        if (data && data.code === 0) {
          this.dataForm.pictureName = data.pictureName
          this.dataForm.pictureUrl = data.imgUrl
        } else {
          this.$message.error(data.msg)
        }
        this.imageUrl = URL.createObjectURL(file.raw)
      },
      beforeAvatarUpload (file) {
        const isJPG = file.type === 'image/jpeg'
        const isLt2M = file.size / 1024 / 1024 < 10
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 10MB!')
        }
        return isJPG && isLt2M
      },
      // 获取医院列表
      getHospitalList () {
        this.$http({
          url: this.$http.adornUrl('/basic/hospital/info'),
          method: 'post',
          data: this.$http.adornData({
            'page': 1,
            'limit': 1000,
            'key': ''
          })
        }).then(({ data }) => {
          if (data.data && data.code === 0) {
            this.hospitalList = data.data.list
            // 默认选择第一个医院
            if (this.hospitalList.length > 0 && !this.dataForm.id) {
              this.dataForm.hospitalId = this.hospitalList[0].id
            }
          } else {
            this.hospitalList = []
          }
        })
      }
    }
  }
</script>
