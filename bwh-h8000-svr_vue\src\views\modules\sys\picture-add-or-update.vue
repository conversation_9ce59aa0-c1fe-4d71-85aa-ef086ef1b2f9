<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="图片名称" prop="pictureName">
      <el-input :disabled = "true" v-model="dataForm.pictureName" placeholder="请上传图片，自动生成"></el-input>
    </el-form-item>
    <el-form-item label="假日名称" prop="holidayName">
      <el-input v-model="dataForm.holidayName" placeholder="假日名称"></el-input>
    </el-form-item>
    <el-form-item label="开始时间" prop="holidayBegtime">
      <el-date-picker v-model="dataForm.pickTime" type="daterange" unlink-panels range-separator=""
          value-format="yyyy-MM-dd" start-placeholder="节日开始时间" end-placeholder="节日结束时间">
        </el-date-picker>
    </el-form-item>
    <el-form-item label="图片地址" prop="pictureUrl">
      <el-upload
      class="avatar-uploader"
      action="http://127.0.0.1:9010/CFMS/sys/picture/savePicture/"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload">
      <i class="el-icon-plus avatar-uploader-icon"></i>
  </el-upload>
      <!-- <el-input v-model="dataForm.pictureUrl" placeholder="图片地址"></el-input> -->
    </el-form-item>
    <div>
      <img style=" width: 200px;
      height: 100px;" v-if="imageUrl" :src="imageUrl" class="avatar">
    </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          pictureUrl: '',
          type: 1,
          pictureName: '',
          holidayName: '',
          holidayBegtime: '',
          holidayEndtime: '',
          operatroId: '',
          createTime: '',
          pickTime: null,
          updateTime: ''
        },
        imageUrl: '',
        dataRule: {
          pictureUrl: [
            { required: true, message: '图片地址不能为空', trigger: 'blur' }
          ],
          pictureName: [
            { required: true, message: '图片名称不能为空', trigger: 'blur' }
          ],
          holidayName: [
            { required: true, message: '假日名称不能为空', trigger: 'blur' }
          ],
          operatroId: [
            { required: true, message: '操作人iD不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        console.log(11111)
        this.dataForm.id = id || 0
        this.visible = true
        this.imageUrl = ''
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/sys/picture/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              console.log(data)
              if (data && data.code === 0) {
                console.log(data)
                this.dataForm.pictureUrl = data.sysPicture.pictureUrl
                this.dataForm.pictureName = data.sysPicture.pictureName
                this.dataForm.holidayName = data.sysPicture.holidayName
                this.dataForm.holidayBegtime = data.sysPicture.holidayBegtime
                this.dataForm.holidayEndtime = data.sysPicture.holidayEndtime
                this.dataForm.pickTime = [data.sysPicture.holidayEndtime, data.sysPicture.holidayEndtime]
                this.dataForm.operatroId = data.sysPicture.operatroId
                this.dataForm.createTime = data.sysPicture.createTime
                this.dataForm.updateTime = data.sysPicture.updateTime
                this.dataForm.type = data.sysPicture.type
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/sys/picture/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'pictureUrl': this.dataForm.pictureUrl,
                'pictureName': this.dataForm.pictureName,
                'holidayName': this.dataForm.holidayName,
                'holidayBegtime': this.dataForm.pickTime === null ? '' : this.dataForm.pickTime[0] + ' 00:00:00',
                'holidayEndtime': this.dataForm.pickTime === null ? '' : this.dataForm.pickTime[1] + ' 23:59:59',
                'operatroId': this.dataForm.operatroId,
                'createTime': this.dataForm.createTime,
                'updateTime': this.dataForm.updateTime
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      handleAvatarSuccess (data, file) {
        if (data && data.code === 0) {
          this.dataForm.pictureName = data.pictureName
          this.dataForm.pictureUrl = data.imgUrl
        } else {
          this.$message.error(data.msg)
        }
        this.imageUrl = URL.createObjectURL(file.raw)
      },
      beforeAvatarUpload (file) {
        const isJPG = file.type === 'image/jpeg'
        const isLt2M = file.size / 1024 / 1024 < 10
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 10MB!')
        }
        return isJPG && isLt2M
      }
    }
  }
</script>
