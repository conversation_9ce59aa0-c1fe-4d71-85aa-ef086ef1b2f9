<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptKey" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="请输入设备名称或序列号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="设备名称">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" label="设备编码">
      </el-table-column>
      <el-table-column prop="serialNum" header-align="center" align="center" label="序列号">
      </el-table-column>
      <el-table-column prop="address" header-align="center" align="center" label="设备地址">
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="所属科室">
      </el-table-column>
      <el-table-column prop="expirationTime" header-align="center" align="center" label="设备到期时间">
      </el-table-column>
      <el-table-column prop="arcFaceActiveState" header-align="center" align="center" label="人脸激活状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.arcFaceActiveState === 1" type="success">已激活</el-tag>
          <el-tag v-else type="danger">未激活</el-tag>
        </template>
      </el-table-column>
      <!-- 激活码配置状态，判断是否为空 -->
      <el-table-column prop="arcFaceActiveKey" header-align="center" align="center" label="人脸激活码配置">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.arcFaceActiveKey" type="success" @click="showArcFaceDialog(scope.row)">已配置</el-tag>
          <el-tag v-else type="danger" @click="showArcFaceDialog(scope.row)">未配置</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="detailNum" header-align="center" align="center" label="子柜数量">
      </el-table-column>
      <!-- heartState表示最新心跳包时间，计算当前时间与heartState的差值，如果差值大于10秒钟，则认为设备离线 -->
      <el-table-column prop="heartState" header-align="center" align="center" width="100" label="在线状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.heartState && getTimeDiff(scope.row.heartState) <= 10" type="success">在线</el-tag>
          <el-tag v-else type="danger">离线</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="100" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="showDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 人脸激活弹窗 -->
    <el-dialog title="人脸激活配置" :visible.sync="arcFaceVisible" width="500px">
      <el-form :model="arcFaceForm" label-width="100px">
        <el-form-item label="激活码">
          <el-input v-model="arcFaceForm.key" placeholder="请输入激活码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="arcFaceVisible = false">取消</el-button>
        <el-button type="primary" @click="submitArcFace">确定</el-button>
      </span>
    </el-dialog>

    <!-- 设备详情弹窗 -->
    <el-dialog title="设备详情" :visible.sync="detailVisible" width="80%" :close-on-click-modal="false">
      <device-detail v-if="detailVisible" :device-info="currentDevice" :device-id="currentDeviceId"></device-detail>
    </el-dialog>
  </div>
</template>

<script>
  import DeviceDetail from './device-detail.vue'

  export default {
    components: {
      DeviceDetail
    },
    data () {
      return {
        dataForm: {
          key: '',
          deptKey: ''
        },
        dataList: [],
        deptList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        arcFaceVisible: false,
        arcFaceForm: {
          id: 0,
          key: ''
        },
        detailVisible: false,
        currentDevice: {},
        currentDeviceId: 0
      }
    },
    created () {
      this.getDeptList()
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/device/info'),
          method: 'post',
          data: this.$http.adornData({
            'key': this.dataForm.key,
            'deptKey': this.dataForm.deptKey,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data.data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({})
        }).then(({ data }) => {
          if (data.data && data.code === 0) {
            this.deptList = data.data.list
            console.log('科室列表加载成功:', this.deptList)
          } else {
            this.deptList = []
            console.warn('科室列表加载失败或为空')
          }
        }).catch(error => {
          console.error('科室列表请求失败:', error)
          this.deptList = []
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 显示详情
      showDetail (row) {
        this.currentDevice = row
        this.currentDeviceId = row.id
        this.detailVisible = true
      },
      // 显示人脸激活弹窗
      showArcFaceDialog (row) {
        this.arcFaceForm = {
          id: row.id,
          key: row.arcFaceActiveKey || ''
        }
        this.arcFaceVisible = true
      },
      // 提交人脸激活
      submitArcFace () {
        this.$http({
          url: this.$http.adornUrl('/device/updateArcFaceInfo'),
          method: 'post',
          data: this.$http.adornData(this.arcFaceForm)
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.arcFaceVisible = false
            this.getDataList()
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      // 计算时间差值（秒）
      getTimeDiff (heartState) {
        if (!heartState) return 999
        // 将时间字符串转换为时间戳
        const heartTime = new Date(heartState.replace(/-/g, '/')).getTime()
        const nowTime = new Date().getTime()
        // 计算时间差（秒）
        return Math.floor((nowTime - heartTime) / 1000)
      }
    }
  }
</script>
