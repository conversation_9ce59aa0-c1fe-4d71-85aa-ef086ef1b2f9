<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="100px">
      <el-form-item label="耗材分类" prop="dicId">
        <el-select v-model="dataForm.dicId" placeholder="请选择耗材分类" clearable>
          <el-option
            v-for="item in dicList"
            :key="item.id"
            :label="item.resName"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="科室" prop="deptId">
        <el-select v-model="dataForm.deptId" placeholder="请选择科室" clearable>
          <el-option
            v-for="item in deptList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="耗材编码" prop="resEpc">
        <el-input v-model="dataForm.resEpc" placeholder="请输入耗材编码"></el-input>
      </el-form-item>
      <el-form-item label="SPD编码" prop="spdCode">
        <el-input v-model="dataForm.spdCode" placeholder="请输入SPD编码"></el-input>
      </el-form-item>
      <!-- 批次 -->
      <el-form-item label="批次" prop="resBatchNo">
        <el-input v-model="dataForm.resBatchNo" placeholder="请输入批次"></el-input>
      </el-form-item>
      <!-- 流转状态，下拉框 -->
      <el-form-item label="流转状态" prop="cirStatus">
        <el-select v-model="dataForm.cirStatus" placeholder="请选择流转状态" clearable>
            <el-option v-for="item in cirStatusList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生产日期" prop="produceDate">
        <el-date-picker
          v-model="dataForm.produceDate"
          type="datetime"
          placeholder="选择生产日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="'00:00:00'">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="有效日期" prop="expiredDate">
        <el-date-picker
          v-model="dataForm.expiredDate"
          type="datetime"
          placeholder="选择有效日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="'23:59:59'">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="操作人" prop="opearId">
        <el-select v-model="dataForm.opearId" placeholder="请选择操作人" clearable>
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          resEpc: '',
          spdCode: '',
          dicId: '',
          resMemo: '',
          expiredDate: '',
          cirStatus: '',
          produceDate: '',
          operaId: '',
          resUdi: '',
          isCheck: '',
          isInside: '',
          deptId: ''
        },
        dicList: [],
        userList: [],
        deptList: [],
        cirStatusList: [
          { id: 0, name: '取出' },
          { id: 1, name: '存入' },
          { id: 2, name: '入库' },
          { id: 3, name: '退货' },
          { id: 4, name: '未入库' },
          { id: 5, name: '待定' },
          { id: 6, name: '驳回退货' },
          { id: 7, name: '消耗' },
          { id: 8, name: '取消消耗' },
          { id: 9, name: '物品下架' },
          { id: 10, name: '更新供应商信息' },
          { id: 11, name: '手工消耗' }
        ],
        dataRule: {
          resEpc: [
            { required: true, message: '耗材编码不能为空', trigger: 'blur' }
          ],
          spdCode: [
            { required: true, message: 'SPD编码不能为空', trigger: 'blur' }
          ],
          dicId: [
            { required: true, message: '耗材分类不能为空', trigger: 'change' }
          ],
          isUse: [
            { required: true, message: '是否启用不能为空', trigger: 'blur' }
          ],
          expiredDate: [
            { required: true, message: '有效日期不能为空', trigger: 'change' }
          ],
          deptId: [
            { required: true, message: '科室不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (row) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (row) {
            this.dataForm = {
              ...this.dataForm,
              ...row
            }
          } else {
            this.dataForm = {
              id: 0,
              resEpc: '',
              spdCode: '',
              dicId: '',
              expiredDate: '',
              resBatchNo: '',
              produceDate: '',
              operaId: '',
              deptId: '',
              cirStatus: 4
            }
          }
        })
        this.getDicList()
        this.getUserList()
        this.getDeptList()
      },
      // 获取物品字典列表
      getDicList () {
        this.$http({
          url: this.$http.adornUrl('/consumable/resBasicDic/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dicList = data.data.list
          } else {
            this.dicList = []
          }
        })
      },
      // 获取用户列表
      getUserList () {
        this.$http({
          url: this.$http.adornUrl('/basic/user/info'),
          method: 'post',
          data: this.$http.adornData({
            'deptKey': this.dataForm.deptId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.userList = data.data.list
          } else {
            this.userList = []
          }
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.deptList = data.data.list
          } else {
            this.deptList = []
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/consumable/resBasicInfo/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
