<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="主柜ID" prop="deviceId">
      <el-input v-model="dataForm.deviceId" placeholder="主柜ID"></el-input>
    </el-form-item>
    <el-form-item label="柜号" prop="deviceDetailCode">
      <el-input v-model="dataForm.deviceDetailCode" placeholder="柜号"></el-input>
    </el-form-item>
    <el-form-item label="子柜名称" prop="deviceDetailName">
      <el-input v-model="dataForm.deviceDetailName" placeholder="子柜名称"></el-input>
    </el-form-item>
    <el-form-item label="机柜mac" prop="deviceMac">
      <el-input v-model="dataForm.deviceMac" placeholder="机柜mac"></el-input>
    </el-form-item>
    <el-form-item label="是否启用" prop="isUse">
      <el-input v-model="dataForm.isUse" placeholder="是否启用"></el-input>
    </el-form-item>
    <el-form-item label="读写器IP" prop="readerIp">
      <el-input v-model="dataForm.readerIp" placeholder="读写器IP"></el-input>
    </el-form-item>
    <el-form-item label="人脸机信息" prop="deviceMemo">
      <el-input v-model="dataForm.deviceMemo" placeholder="人脸机信息"></el-input>
    </el-form-item>
    <el-form-item label="控制板IP" prop="controlIp">
      <el-input v-model="dataForm.controlIp" placeholder="控制板IP"></el-input>
    </el-form-item>
    <el-form-item label="是否主柜 1：主柜 0副柜" prop="isMain">
      <el-input v-model="dataForm.isMain" placeholder="是否主柜 1：主柜 0副柜"></el-input>
    </el-form-item>
    <el-form-item label="人脸机ip" prop="faceMachineIp">
      <el-input v-model="dataForm.faceMachineIp" placeholder="人脸机ip"></el-input>
    </el-form-item>
    <el-form-item label="IP柜内网IP（弃用）" prop="deviceIp">
      <el-input v-model="dataForm.deviceIp" placeholder="IP柜内网IP（弃用）"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="修改时间" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder="修改时间"></el-input>
    </el-form-item>
    <el-form-item label="控制板mac" prop="controlMac">
      <el-input v-model="dataForm.controlMac" placeholder="控制板mac"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          deviceId: '',
          deviceDetailCode: '',
          deviceDetailName: '',
          deviceMac: '',
          isUse: '',
          readerIp: '',
          deviceMemo: '',
          controlIp: '',
          isMain: '',
          faceMachineIp: '',
          deviceIp: '',
          createTime: '',
          updateTime: '',
          controlMac: ''
        },
        dataRule: {
          deviceId: [
            { required: true, message: '主柜ID不能为空', trigger: 'blur' }
          ],
          deviceDetailCode: [
            { required: true, message: '柜号不能为空', trigger: 'blur' }
          ],
          deviceDetailName: [
            { required: true, message: '子柜名称不能为空', trigger: 'blur' }
          ],
          deviceMac: [
            { required: true, message: '机柜mac不能为空', trigger: 'blur' }
          ],
          isUse: [
            { required: true, message: '是否启用不能为空', trigger: 'blur' }
          ],
          readerIp: [
            { required: true, message: '读写器IP不能为空', trigger: 'blur' }
          ],
          deviceMemo: [
            { required: true, message: '人脸机信息不能为空', trigger: 'blur' }
          ],
          controlIp: [
            { required: true, message: '控制板IP不能为空', trigger: 'blur' }
          ],
          isMain: [
            { required: true, message: '是否主柜 1：主柜 0副柜不能为空', trigger: 'blur' }
          ],
          faceMachineIp: [
            { required: true, message: '人脸机ip不能为空', trigger: 'blur' }
          ],
          deviceIp: [
            { required: true, message: 'IP柜内网IP（弃用）不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '修改时间不能为空', trigger: 'blur' }
          ],
          controlMac: [
            { required: true, message: '控制板mac不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/generator/cfmsdevicedetail/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.deviceId = data.cfmsDeviceDetail.deviceId
                this.dataForm.deviceDetailCode = data.cfmsDeviceDetail.deviceDetailCode
                this.dataForm.deviceDetailName = data.cfmsDeviceDetail.deviceDetailName
                this.dataForm.deviceMac = data.cfmsDeviceDetail.deviceMac
                this.dataForm.isUse = data.cfmsDeviceDetail.isUse
                this.dataForm.readerIp = data.cfmsDeviceDetail.readerIp
                this.dataForm.deviceMemo = data.cfmsDeviceDetail.deviceMemo
                this.dataForm.controlIp = data.cfmsDeviceDetail.controlIp
                this.dataForm.isMain = data.cfmsDeviceDetail.isMain
                this.dataForm.faceMachineIp = data.cfmsDeviceDetail.faceMachineIp
                this.dataForm.deviceIp = data.cfmsDeviceDetail.deviceIp
                this.dataForm.createTime = data.cfmsDeviceDetail.createTime
                this.dataForm.updateTime = data.cfmsDeviceDetail.updateTime
                this.dataForm.controlMac = data.cfmsDeviceDetail.controlMac
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/generator/cfmsdevicedetail/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'deviceId': this.dataForm.deviceId,
                'deviceDetailCode': this.dataForm.deviceDetailCode,
                'deviceDetailName': this.dataForm.deviceDetailName,
                'deviceMac': this.dataForm.deviceMac,
                'isUse': this.dataForm.isUse,
                'readerIp': this.dataForm.readerIp,
                'deviceMemo': this.dataForm.deviceMemo,
                'controlIp': this.dataForm.controlIp,
                'isMain': this.dataForm.isMain,
                'faceMachineIp': this.dataForm.faceMachineIp,
                'deviceIp': this.dataForm.deviceIp,
                'createTime': this.dataForm.createTime,
                'updateTime': this.dataForm.updateTime,
                'controlMac': this.dataForm.controlMac
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
