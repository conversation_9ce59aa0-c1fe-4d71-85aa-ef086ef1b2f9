<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="设备编码" prop="code">
      <el-input v-model="dataForm.code" placeholder="设备编码"></el-input>
    </el-form-item>
    <el-form-item label="设备名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="设备名称"></el-input>
    </el-form-item>
    <el-form-item label="设备类型id" prop="typeId">
      <el-input v-model="dataForm.typeId" placeholder="设备类型id"></el-input>
    </el-form-item>
    <el-form-item label="设备地址" prop="address">
      <el-input v-model="dataForm.address" placeholder="设备地址"></el-input>
    </el-form-item>
    <el-form-item label="子柜数量" prop="detailNum">
      <el-input v-model="dataForm.detailNum" placeholder="子柜数量"></el-input>
    </el-form-item>
    <el-form-item label="科室ID" prop="deptId">
      <el-input v-model="dataForm.deptId" placeholder="科室ID"></el-input>
    </el-form-item>
    <el-form-item label="医院ID" prop="hospitalId">
      <el-input v-model="dataForm.hospitalId" placeholder="医院ID"></el-input>
    </el-form-item>
    <el-form-item label="仓库ID" prop="warehouseId">
      <el-input v-model="dataForm.warehouseId" placeholder="仓库ID"></el-input>
    </el-form-item>
    <el-form-item label="是否启用" prop="isUse">
      <el-input v-model="dataForm.isUse" placeholder="是否启用"></el-input>
    </el-form-item>
    <el-form-item label="(内网)子网掩码" prop="lanSubmask">
      <el-input v-model="dataForm.lanSubmask" placeholder="(内网)子网掩码"></el-input>
    </el-form-item>
    <el-form-item label="(内网)网关" prop="lanGateway">
      <el-input v-model="dataForm.lanGateway" placeholder="(内网)网关"></el-input>
    </el-form-item>
    <el-form-item label="(内网)ip" prop="lanIp">
      <el-input v-model="dataForm.lanIp" placeholder="(内网)ip"></el-input>
    </el-form-item>
    <el-form-item label="机柜mac" prop="mac">
      <el-input v-model="dataForm.mac" placeholder="机柜mac"></el-input>
    </el-form-item>
    <el-form-item label="（外网）机柜IP" prop="ip">
      <el-input v-model="dataForm.ip" placeholder="（外网）机柜IP"></el-input>
    </el-form-item>
    <el-form-item label="（外网）网关" prop="gateway">
      <el-input v-model="dataForm.gateway" placeholder="（外网）网关"></el-input>
    </el-form-item>
    <el-form-item label="（外网）子网掩码" prop="submask">
      <el-input v-model="dataForm.submask" placeholder="（外网）子网掩码"></el-input>
    </el-form-item>
    <el-form-item label="序列号" prop="serialNum">
      <el-input v-model="dataForm.serialNum" placeholder="序列号"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="修改时间" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder="修改时间"></el-input>
    </el-form-item>
    <el-form-item label="设备过期时间" prop="expirationTime">
      <el-input v-model="dataForm.expirationTime" placeholder="设备过期时间"></el-input>
    </el-form-item>
    <el-form-item label="录像机IP" prop="videoIp">
      <el-input v-model="dataForm.videoIp" placeholder="录像机IP"></el-input>
    </el-form-item>
    <el-form-item label="ARC人脸算法激活码" prop="arcFaceActiveKey">
      <el-input v-model="dataForm.arcFaceActiveKey" placeholder="ARC人脸算法激活码"></el-input>
    </el-form-item>
    <el-form-item label="ARC人脸算法激活状态" prop="arcFaceActiveState">
      <el-input v-model="dataForm.arcFaceActiveState" placeholder="ARC人脸算法激活状态"></el-input>
    </el-form-item>
    <el-form-item label="ARC人脸算法激活时间" prop="arcFaceActiveTime">
      <el-input v-model="dataForm.arcFaceActiveTime" placeholder="ARC人脸算法激活时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          code: '',
          name: '',
          typeId: '',
          address: '',
          detailNum: '',
          deptId: '',
          hospitalId: '',
          warehouseId: '',
          isUse: '',
          lanSubmask: '',
          lanGateway: '',
          lanIp: '',
          mac: '',
          ip: '',
          gateway: '',
          submask: '',
          serialNum: '',
          createTime: '',
          updateTime: '',
          expirationTime: '',
          videoIp: '',
          arcFaceActiveKey: '',
          arcFaceActiveState: '',
          arcFaceActiveTime: ''
        },
        dataRule: {
          code: [
            { required: true, message: '设备编码不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '设备名称不能为空', trigger: 'blur' }
          ],
          typeId: [
            { required: true, message: '设备类型id不能为空', trigger: 'blur' }
          ],
          address: [
            { required: true, message: '设备地址不能为空', trigger: 'blur' }
          ],
          detailNum: [
            { required: true, message: '子柜数量不能为空', trigger: 'blur' }
          ],
          deptId: [
            { required: true, message: '科室ID不能为空', trigger: 'blur' }
          ],
          hospitalId: [
            { required: true, message: '医院ID不能为空', trigger: 'blur' }
          ],
          warehouseId: [
            { required: true, message: '仓库ID不能为空', trigger: 'blur' }
          ],
          isUse: [
            { required: true, message: '是否启用不能为空', trigger: 'blur' }
          ],
          lanSubmask: [
            { required: true, message: '(内网)子网掩码不能为空', trigger: 'blur' }
          ],
          lanGateway: [
            { required: true, message: '(内网)网关不能为空', trigger: 'blur' }
          ],
          lanIp: [
            { required: true, message: '(内网)ip不能为空', trigger: 'blur' }
          ],
          mac: [
            { required: true, message: '机柜mac不能为空', trigger: 'blur' }
          ],
          ip: [
            { required: true, message: '（外网）机柜IP不能为空', trigger: 'blur' }
          ],
          gateway: [
            { required: true, message: '（外网）网关不能为空', trigger: 'blur' }
          ],
          submask: [
            { required: true, message: '（外网）子网掩码不能为空', trigger: 'blur' }
          ],
          serialNum: [
            { required: true, message: '序列号不能为空', trigger: 'blur' }
          ],
          createTime: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '修改时间不能为空', trigger: 'blur' }
          ],
          expirationTime: [
            { required: true, message: '设备过期时间不能为空', trigger: 'blur' }
          ],
          videoIp: [
            { required: true, message: '录像机IP不能为空', trigger: 'blur' }
          ],
          arcFaceActiveKey: [
            { required: true, message: 'ARC人脸算法激活码不能为空', trigger: 'blur' }
          ],
          arcFaceActiveState: [
            { required: true, message: 'ARC人脸算法激活状态不能为空', trigger: 'blur' }
          ],
          arcFaceActiveTime: [
            { required: true, message: 'ARC人脸算法激活时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/generator/cfmsdevice/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.code = data.cfmsDevice.code
                this.dataForm.name = data.cfmsDevice.name
                this.dataForm.typeId = data.cfmsDevice.typeId
                this.dataForm.address = data.cfmsDevice.address
                this.dataForm.detailNum = data.cfmsDevice.detailNum
                this.dataForm.deptId = data.cfmsDevice.deptId
                this.dataForm.hospitalId = data.cfmsDevice.hospitalId
                this.dataForm.warehouseId = data.cfmsDevice.warehouseId
                this.dataForm.isUse = data.cfmsDevice.isUse
                this.dataForm.lanSubmask = data.cfmsDevice.lanSubmask
                this.dataForm.lanGateway = data.cfmsDevice.lanGateway
                this.dataForm.lanIp = data.cfmsDevice.lanIp
                this.dataForm.mac = data.cfmsDevice.mac
                this.dataForm.ip = data.cfmsDevice.ip
                this.dataForm.gateway = data.cfmsDevice.gateway
                this.dataForm.submask = data.cfmsDevice.submask
                this.dataForm.serialNum = data.cfmsDevice.serialNum
                this.dataForm.createTime = data.cfmsDevice.createTime
                this.dataForm.updateTime = data.cfmsDevice.updateTime
                this.dataForm.expirationTime = data.cfmsDevice.expirationTime
                this.dataForm.videoIp = data.cfmsDevice.videoIp
                this.dataForm.arcFaceActiveKey = data.cfmsDevice.arcFaceActiveKey
                this.dataForm.arcFaceActiveState = data.cfmsDevice.arcFaceActiveState
                this.dataForm.arcFaceActiveTime = data.cfmsDevice.arcFaceActiveTime
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/generator/cfmsdevice/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'code': this.dataForm.code,
                'name': this.dataForm.name,
                'typeId': this.dataForm.typeId,
                'address': this.dataForm.address,
                'detailNum': this.dataForm.detailNum,
                'deptId': this.dataForm.deptId,
                'hospitalId': this.dataForm.hospitalId,
                'warehouseId': this.dataForm.warehouseId,
                'isUse': this.dataForm.isUse,
                'lanSubmask': this.dataForm.lanSubmask,
                'lanGateway': this.dataForm.lanGateway,
                'lanIp': this.dataForm.lanIp,
                'mac': this.dataForm.mac,
                'ip': this.dataForm.ip,
                'gateway': this.dataForm.gateway,
                'submask': this.dataForm.submask,
                'serialNum': this.dataForm.serialNum,
                'createTime': this.dataForm.createTime,
                'updateTime': this.dataForm.updateTime,
                'expirationTime': this.dataForm.expirationTime,
                'videoIp': this.dataForm.videoIp,
                'arcFaceActiveKey': this.dataForm.arcFaceActiveKey,
                'arcFaceActiveState': this.dataForm.arcFaceActiveState,
                'arcFaceActiveTime': this.dataForm.arcFaceActiveTime
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
