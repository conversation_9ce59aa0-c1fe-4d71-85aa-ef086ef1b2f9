<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="医院代码" prop="code">
      <el-input v-model="dataForm.code" placeholder="请输入医院代码"></el-input>
    </el-form-item>
    <el-form-item label="医院名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="请输入医院名称"></el-input>
    </el-form-item>
    <el-form-item label="医院位置" prop="address">
      <el-input v-model="dataForm.address" placeholder="请输入医院位置"></el-input>
    </el-form-item>
    <el-form-item label="医院拼音" prop="py">
      <el-input v-model="dataForm.py" placeholder="请输入医院拼音"></el-input>
    </el-form-item>
    <!-- <el-form-item label="状态" prop="isUse">
      <el-radio-group v-model="dataForm.isUse">
        <el-radio :label="1">启用</el-radio>
        <el-radio :label="0">禁用</el-radio>
      </el-radio-group>
    </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          code: '',
          name: '',
          isUse: 1,
          address: '',
          py: ''
        },
        dataRule: {
          code: [
            { required: true, message: '医院代码不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '医院名称不能为空', trigger: 'blur' }
          ],
          isUse: [
            { required: true, message: '请选择状态', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (row) {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (row) {
            this.dataForm = {
              ...this.dataForm,
              ...row
            }
          } else {
            this.dataForm = {
              id: 0,
              code: '',
              name: '',
              isUse: 1,
              address: '',
              py: ''
            }
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/basic/hospital/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
