<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptId" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="dataForm.deviceId" placeholder="请选择设备" clearable @click.native="getDeviceList">
          <el-option v-for="item in deviceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-input v-model="dataForm.resName" placeholder="请输入物品名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="resName" header-align="center" align="center" label="物品名称">
        <template slot-scope="scope">
          {{ scope.row.resName ? scope.row.resName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="科室名称">
        <template slot-scope="scope">
          {{ scope.row.deptName ? scope.row.deptName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deptCode" header-align="center" align="center" label="科室代码">
        <template slot-scope="scope">
          {{ scope.row.deptCode ? scope.row.deptCode : '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="deviceDetailName" header-align="center" align="center" label="设备名称">
        <template slot-scope="scope">
          {{ scope.row.deviceDetailName ? scope.row.deviceDetailName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceDetailCode" header-align="center" align="center" label="设备编码">
        <template slot-scope="scope">
          {{ scope.row.deviceDetailCode ? scope.row.deviceDetailCode : '-' }}
        </template>
      </el-table-column> -->
      <el-table-column prop="currentNum" header-align="center" align="center" label="当前库存">
        <template slot-scope="scope">
          {{ scope.row.currentNum ? scope.row.currentNum : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="downLine" header-align="center" align="center" label="下限值">
        <template slot-scope="scope">
          {{ scope.row.downLine ? scope.row.downLine : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="upLine" header-align="center" align="center" label="上限值">
        <template slot-scope="scope">
          {{ scope.row.upLine ? scope.row.upLine : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resTrans" header-align="center" align="center" label="包装系数">
        <template slot-scope="scope">
          {{ scope.row.resTrans ? scope.row.resTrans : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resBatchNo" header-align="center" align="center" label="批号">
        <template slot-scope="scope">
          {{ scope.row.resBatchNo ? scope.row.resBatchNo : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="supName" header-align="center" align="center" label="供应商名称">
        <template slot-scope="scope">
          {{ scope.row.supName ? scope.row.supName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resDw" header-align="center" align="center" label="单位">
        <template slot-scope="scope">
          {{ scope.row.resDw ? scope.row.resDw : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="vacancy" header-align="center" align="center" label="缺口/超出数量">
        <template slot-scope="scope">
          <el-tag :type="getVacancyType(scope.row.type)">
            {{ getVacancyText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          resName: '',
          deptId: '',
          page: 1,
          limit: 10
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        deptList: [],
        deviceList: []
      }
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/infoSelect/restock/query'),
          method: 'post',
          data: this.$http.adornData({
            'resName': this.dataForm.resName,
            'deptId': this.dataForm.deptId,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({})
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deptList = data.data.list
          } else {
            this.deptList = []
          }
        })
      },
      // // 获取设备列表
      // getDeviceList () {
      //   // 清空设备列表
      //   this.deviceList = []
      //   this.dataForm.deviceId = ''
      //   this.$http({
      //     url: this.$http.adornUrl('/device/info'),
      //     method: 'post',
      //     data: this.$http.adornData({
      //       'deptKey': this.dataForm.deptId
      //     })
      //   }).then(({ data }) => {
      //     if (data && data.code === 0) {
      //       this.deviceList = data.data.list
      //     } else {
      //       this.deviceList = []
      //     }
      //   })
      // },
      // 获取缺口/超出数量标签样式
      getVacancyType (type) {
        return type === 0 ? 'danger' : 'warning'
      },
      // 获取缺口/超出数量文本
      getVacancyText (row) {
        if (row.type === 0) {
          return `缺口${row.vacancy}`
        } else {
          return `超出${row.vacancy}`
        }
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script>
