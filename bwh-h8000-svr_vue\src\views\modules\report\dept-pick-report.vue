<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptKey" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.type" placeholder="请选择报表操作类型" clearable>
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleTimeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="exportExcel" :disabled="!dataList.length">导出Excel</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%">
      <el-table-column fixed prop="resName" header-align="center" align="center" label="物品名称">
        <template slot-scope="scope">
          {{ scope.row.resName || '未知物品' }}
        </template>
      </el-table-column>

      <!-- 根据日期生成对应的列 -->
      <el-table-column label="日期">
        <el-table-column v-for="(item, index) in dateColumns" :key="index" :label="item.split('-')[1] + '-' + item.split('-')[2]" header-align="center" align="center">
          <template slot-scope="scope">
            {{ getDayCount(scope.row, item) }}
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="合计" align="center" fixed="right">
        <template slot-scope="scope">
          {{ getTotalCount(scope.row) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { exportDeptPickReport } from '@/utils/excel'
  export default {
    data () {
      return {
        dataForm: {
          deptKey: '',
          type: null,
          startTime: '',
          endTime: ''
        },
        timeRange: [],
        dataList: [],
        dataListLoading: false,
        deptList: [],
        dateColumns: [],
        typeList: [
          { value: 0, label: '取出' },
          { value: 1, label: '存入' },
          { value: 2, label: '入库' },
          { value: 3, label: '退货' },
          { value: 4, label: '未入库' },
          { value: 5, label: '待定' },
          { value: 6, label: '驳回退货' },
          { value: 7, label: '消耗' },
          { value: 8, label: '取消消耗' },
          { value: 9, label: '物品下架' },
          { value: 10, label: '更新供应商信息' },
          { value: 11, label: '手工消耗' }
        ]
      }
    },
    activated () {
      // this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        // 科室、类型、时间范围
        if (!this.dataForm.deptKey || this.dataForm.type === null || !this.dataForm.startTime || !this.dataForm.endTime) {
          this.$message.error('请选择科室、类型、时间范围')
          return
        }

        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/report/info'),
          method: 'post',
          data: this.$http.adornData({
            'deptKey': this.dataForm.deptKey,
            'type': this.dataForm.type,
            'startTime': this.dataForm.startTime,
            'endTime': this.dataForm.endTime
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            // 过滤未知物品
            const serviceData = data.data.filter(item => item.resName !== null)
            this.updateDateColumns(serviceData)

            // 在数据最后添加一个合计行
            serviceData.push({
              resName: '合计',
              listByDay: this.dateColumns.map(date => ({
                day: date,
                count: serviceData.reduce((total, item) => total + this.getDayCount(item, date), 0)
              }))
            })
            this.dataList = serviceData
          } else {
            this.dataList = []
            this.dateColumns = []
          }
          this.dataListLoading = false
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deptList = data.data.list
          } else {
            this.deptList = []
          }
        })
      },
      // 处理时间范围变化
      handleTimeChange (val) {
        if (val) {
          // 时间差不能大于31天
          const timeDiff = new Date(val[1]) - new Date(val[0])
          if (timeDiff > 31 * 24 * 60 * 60 * 1000) {
            this.$message.error('时间范围不能大于31天')
            this.timeRange = []
            this.dataForm.startTime = ''
            this.dataForm.endTime = ''
            return
          }
          this.dataForm.startTime = val[0]
          this.dataForm.endTime = val[1]
        } else {
          this.dataForm.startTime = ''
          this.dataForm.endTime = ''
        }
      },
      // 更新日期列
      updateDateColumns (allData) {
        const dates = new Set()
        allData.forEach(item => {
          item.listByDay.forEach(day => {
            dates.add(day.day)
          })
        })
        this.dateColumns = Array.from(dates).sort()
      },
      // 获取某天的数量
      getDayCount (row, date) {
        const dayData = row.listByDay.find(item => item.day === date)
        return dayData ? dayData.count : 0
      },
      // 获取合计数量
      getTotalCount (row) {
        return row.listByDay.reduce((total, item) => total + item.count, 0)
      },
      // 导出Excel
      exportExcel () {
        if (!this.dataList.length) {
          this.$message.warning('暂无数据可导出')
          return
        }
        exportDeptPickReport(this.dataList, this.dateColumns, this.deptList, this.dataForm, this.typeList)
      }
    }
  }
</script>
