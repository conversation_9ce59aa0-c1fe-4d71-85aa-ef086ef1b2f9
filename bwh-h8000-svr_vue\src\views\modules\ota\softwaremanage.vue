<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="请输入软件名称" clearable @input="
            e => {
              dataForm.key = validForbid(e, 50);
            }
          " />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button @click="addHandle()" type="primary">新增软件</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="appId" header-align="center" align="center" label="软件ID" />
      <el-table-column prop="appName" header-align="center" align="center" label="软件名称" />
      <el-table-column prop="versionNum" header-align="center" align="center" label="版本数量" />
      <el-table-column prop="createTime" header-align="center" align="center" label="创建时间" />
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="
              showDetail(scope.row.id, scope.row.appName, scope.row.appId)
            ">历史版本</el-button>
          <el-button type="text" size="small" @click="
              addVersion(scope.row.id, scope.row.appName, scope.row.appId)
            ">新增版本</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper" />
    <add @close="getDataList" ref="add" />
    <detail ref="detail" />
    <version-add @close="getDataList" ref="versionAdd" />
  </div>
</template>

<script>
  import Add from './softwaremanage-add'
  import Detail from './softwaremanage-details'
  import VersionAdd from './softwaremanage-versionadd'

  export default {
    data () {
      return {
        dataForm: {
          key: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        addVisible: false,
        detailVisible: false,
        versionAddVisible: false
      }
    },
    components: {
      Add,
      Detail,
      VersionAdd
    },
    activated () {
      this.getDataList()
    },
    computed: {},
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          // TODO 获取软件列表url
          url: this.$http.adornUrl('/ota/appinfo/list'),
          method: 'post',
          data: this.$http.adornParams({
            page: this.pageIndex,
            limit: this.pageSize,
            appName: this.dataForm.key
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              if (data.page.list.length === 0) {
                this.pageIndex = 1
                if (data.page.totalCount > 0) {
                  this.getDataList()
                } else {
                  this.dataList = []
                  this.totalPage = 0
                }
              } else {
                this.dataList = data.page.list
                this.totalPage = data.page.totalCount
              }
            } else {
              this.dataList = []
              this.totalPage = 0
            }
          })
          .finally(() => {
            this.dataListLoading = false
          })
      },
      addHandle () {
        this.addVisible = true
        this.$nextTick(() => {
          this.$refs.add.init()
        })
      },
      showDetail (id, name, appId) {
        this.detailVisible = true
        this.$nextTick(() => {
          this.$refs.detail.init(id, name, appId)
        })
      },
      addVersion (id, name, appId) {
        this.versionAddVisible = true
        this.$nextTick(() => {
          this.$refs.versionAdd.init(id, name, appId)
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script>