<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-select v-model="dataForm.deptId" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.deviceId" placeholder="请选择设备" clearable @click.native="getDeviceList">
          <el-option v-for="item in deviceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.deviceDetailId" placeholder="请选择子柜" clearable @click.native="getDeviceDetailList">
          <el-option v-for="item in deviceDetailList" :key="item.id" :label="item.deviceDetailCode" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.cirStatus" placeholder="请选择异常类型" clearable multiple>
          <el-option v-for="item in illTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList(true)">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="所属科室">
        <template slot-scope="scope">
          {{ scope.row.deptName ? scope.row.deptName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceName" header-align="center" align="center" label="设备名称">
        <template slot-scope="scope">
          {{ scope.row.deviceName ? scope.row.deviceName : '-' }}
        </template>
      </el-table-column>
            <el-table-column prop="deviceName" header-align="center" align="center" label="子柜编码">
        <template slot-scope="scope">
          {{ scope.row.deviceName ? scope.row.deviceDetailCode : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="deviceDetailName" header-align="center" align="center" label="子柜名称">
        <template slot-scope="scope">
          {{ scope.row.deviceDetailName ? scope.row.deviceDetailName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resName" header-align="center" align="center" label="物品名称">
        <template slot-scope="scope">
          {{ scope.row.resName ? scope.row.resName : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resUnit" header-align="center" align="center" label="规格">
        <template slot-scope="scope">
          {{ scope.row.resUnit ? scope.row.resUnit : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resEpc" header-align="center" align="center" label="物品标签唯一码">
      </el-table-column>
      <el-table-column prop="expiredDate" header-align="center" align="center" label="物品截至日期">
        <template slot-scope="scope">
          {{ scope.row.expiredDate ? scope.row.expiredDate : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="resBatchNo" header-align="center" align="center" label="批号">
        <template slot-scope="scope">
          {{ scope.row.resBatchNo ? scope.row.resBatchNo : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="illType" header-align="center" align="center" label="异常类型">
        <template slot-scope="scope">
          {{ getIllTypeText(scope.row.illType) }}
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          deptId: '',
          deviceId: '',
          deviceDetailId: '',
          cirStatus: [],
          page: 1,
          limit: 10
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        deptList: [],
        deviceList: [],
        deviceDetailList: [],
        illTypeList: [
          { value: 0, label: '正常' },
          { value: 1, label: '未知物品' },
          { value: 2, label: '已退货' },
          { value: 3, label: '消耗' },
          { value: 4, label: '科室' }
        ]
      }
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList (resetPage = false) {
        this.dataListLoading = true
        if (resetPage) {
          this.pageIndex = 1
        }
        this.$http({
          url: this.$http.adornUrl('/infoSelect/stockQuery/queryStockAbnormal'),
          method: 'post',
          data: this.$http.adornData({
            'deptId': this.dataForm.deptId,
            'deviceId': this.dataForm.deviceId,
            'deviceDetailId': this.dataForm.deviceDetailId,
            'cirStatus': this.dataForm.cirStatus,
            'page': this.pageIndex,
            'limit': this.pageSize
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.data.list
            this.totalPage = data.data.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 获取科室列表
      getDeptList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deptList = data.data.list
          } else {
            this.deptList = []
          }
        })
      },
      // 获取设备列表
      getDeviceList () {
        this.$http({
          url: this.$http.adornUrl('/device/info'),
          method: 'post',
          data: this.$http.adornData({
            'deptKey': this.dataForm.deptId,
            'page': 1,
            'limit': 1000
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deviceList = data.data.list
            this.getDeviceDetailList()
          } else {
            this.deviceList = []
            this.deviceDetailList = []
          }
        })
      },
      // 获取子柜列表
      getDeviceDetailList () {
        this.$http({
          url: this.$http.adornUrl('/device/getDeviceDetails'),
          method: 'post',
          data: this.$http.adornData({
            'deviceId': this.dataForm.deviceId,
            'page': 1,
            'limit': -1
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.deviceDetailList = data.data.list
          } else {
            this.deviceDetailList = []
          }
        })
      },
      // 获取异常类型文本
      getIllTypeText (type) {
        const item = this.illTypeList.find(item => item.value === type)
        return item ? item.label : '未知物品'
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    },
    watch: {
      'dataForm.deptId' () {
        this.dataForm.deviceId = ''
        this.dataForm.deviceDetailId = ''
        this.deviceList = []
        this.deviceDetailList = []
      },
      'dataForm.deviceId' () {
        this.dataForm.deviceDetailId = ''
        this.getDeviceDetailList()
      }
    }
  }
</script>
