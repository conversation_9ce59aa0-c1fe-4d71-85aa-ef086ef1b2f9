<template>
  <div class="site-wrapper site-page--login">
    <div class="site-content__wrapper">
      <div class="main_title">
        Hello,Welcome back!
      </div>
      <div class="sub_title">
        耗材智能管理系统
      </div>
      <div class="site-content">
        <div class="login-main">
          <el-form :model="dataForm"
                   :rules="dataRule"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()"
                   status-icon>
            <el-form-item prop="userName">
              <el-input class="login-input-user"
                        v-model="dataForm.userName"
                        placeholder="帐号"></el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input class="login-input-password"
                        v-model="dataForm.password"
                        type="password"
                        placeholder="密码"></el-input>
            </el-form-item>
            <el-form-item prop="captcha">
              <el-row :gutter="20">
                <el-col :span="14">
                  <el-input class="login-captcha-code"
                            v-model="dataForm.captcha"
                            placeholder="验证码"></el-input>
                </el-col>
                <el-col :span="10"
                        class="login-captcha">
                  <img :src="captchaPath"
                       @click="getCaptcha()"
                       alt/>
                </el-col>
              </el-row>
            </el-form-item>
            <el-checkbox v-model="dataForm.remember">记住密码</el-checkbox>
            <el-form-item>
              <el-button class="login-btn-submit"
                         type="primary"
                         @click="dataFormSubmit()">登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <footer class="footer">Copyright © 安徽博微智能电气有限公司版权所有</footer>
    </div>

  </div>
</template>

<script>
import {getUUID} from '@/utils'

export default {
  data () {
    return {
      dataForm: {
        userName: this.$cookie.get('userName') || '',
        password: this.$cookie.get('password') || '',
        uuid: '',
        captcha: '',
        remember: this.$cookie.get('remember') === 'true' || false
      },
      dataRule: {
        userName: [
          {required: true, message: '帐号不能为空', trigger: 'blur'}
        ],
        password: [
          {required: true, message: '密码不能为空', trigger: 'blur'}
        ],
        captcha: [
          {required: true, message: '验证码不能为空', trigger: 'blur'}
        ]
      },
      captchaPath: ''
    }
  },
  created () {
    this.getCaptcha()
  },
  methods: {
    // 提交表单
    dataFormSubmit () {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/sys/login'),
            method: 'post',
            data: this.$http.adornData({
              username: this.dataForm.userName,
              password: this.dataForm.password,
              uuid: this.dataForm.uuid,
              captcha: this.dataForm.captcha
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              if (this.dataForm.remember) {
                this.$cookie.set('userName', this.dataForm.userName)
                this.$cookie.set('password', this.dataForm.password)
              } else {
                this.$cookie.delete('userName')
                this.$cookie.delete('password')
              }
              this.$cookie.set('remember', this.dataForm.remember)
              this.$cookie.set('token', data.token)
              this.$router.replace({name: 'home'})
            } else {
              this.getCaptcha()
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 获取验证码
    getCaptcha () {
      this.dataForm.uuid = getUUID()
      this.captchaPath = this.$http.adornUrl(
        `/captcha.jpg?uuid=${this.dataForm.uuid}`
      )
    }
  }
}
</script>

<style lang="scss">
.site-wrapper.site-page--login {
  position: absolute;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-image: url(~@/assets/img/login_bg.jpg);
  background-size: 100% 100%;

  .site-content__wrapper {
    background-color: red;
    position: relative;
    padding: 0;
    margin: 0;
    z-index: 0;
    background-color: transparent;
  }

  .main_title {
    position: relative;
    width: 100%;
    text-align: center;
    padding: 100px 0px 0px; /*no*/
    font-size: 100px;
    color: #bce8f1;
  }

  .sub_title {
    position: relative;
    width: 100%;
    text-align: center;
    padding: 80px 0px 0px; /*no*/
    font-size: 50px;
    color: #bce8f1;
  }

  .footer {
    position: absolute;
    width: 100%;
    text-align: center;
    bottom: 10px;
    font-size: 15px;
  }


  .site-content {
    position: absolute;
    top: 62%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .login-main {
    position: relative;
    padding: 50px 60px 180px; /*no*/
    // left: 690px;
    // top: 380px;
    width: 510px; /*no*/
    height: 348px; /*no*/
    background-size: cover;
    background-image: url(~@/assets/img/login_mian_bg.png);
  }

  .login-input-user {
    .el-input__inner {
      height: 37px; /*no*/
    }
  }

  .login-input-password {
    .el-input__inner {
      height: 37px; /*no*/
    }
  }

  .login-captcha-code {
    .el-input__inner {
      height: 37px; /*no*/
    }
  }

  .login-captcha {
    overflow: hidden;

    > img {
      width: 100%;
      cursor: pointer;
    }
  }

  .login-btn-submit {
    background-color: #42a3fe;
    width: 100%;
    height: 57px; /*no*/
    margin-top: 24px; /*no*/
    font-size: 18px; /*no*/
    letter-spacing: 3px; /*no*/
  }
}
</style>
