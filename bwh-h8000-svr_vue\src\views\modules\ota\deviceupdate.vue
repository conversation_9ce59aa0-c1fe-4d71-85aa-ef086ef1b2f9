<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <!-- 下拉框选择医院 -->
      <el-form-item>
        <el-select filterable @change="hospitalHandleChange" v-model="dataForm.hospitalId" placeholder="请选择医院" clearable
          @click.native="getHospitalList">
          <el-option v-for="item in hospitalList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 下拉框选择科室 -->
      <el-form-item>
        <el-select filterable :disabled="!dataForm.hospitalId" v-model="dataForm.deptId" placeholder="请选择科室" clearable
          @click.native="getRegionList">
          <el-option v-for="item in regionList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select filterable v-model="dataForm.deviceType" placeholder="请选择设备类型" clearable>
          <el-option v-for="item in cabTypeMap" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select filterable multiple collapse-tags v-model="dataForm.updateStatus" placeholder="请选择设备更新状态" clearable>
          <el-option v-for="item in updateStateMap" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.currVersion" placeholder="请输入设备当前版本" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.updateVersion" placeholder="请输入设备配置版本" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.deviceName" placeholder="请输入设备名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.deviceCode" placeholder="请输入设备编码" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList">查 询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="isAuth('ota:device:update')" type="primary" :disabled="multipleSelection.length === 0"
          @click="updateMore">批量配置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="updateLogShow">更新日志</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" header-align="center" align="center" />
      <el-table-column label="序号" width="55" header-align="center" align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="hospitalName" header-align="center" align="center" label="医院">
      </el-table-column>
      <el-table-column prop="deptName" header-align="center" align="center" label="科室">
      </el-table-column>
      <el-table-column prop="deviceName" header-align="center" align="center" label="名称">
      </el-table-column>
      <el-table-column prop="deviceCode" header-align="center" align="center" label="编码">
      </el-table-column>
      <el-table-column prop="deviceType" header-align="center" align="center" width="100" label="类型">
        <template slot-scope="scope">
          {{ scope.row.deviceType && cabTypeMap[scope.row.deviceType].label }}
        </template>
      </el-table-column>
      <!-- heartState表示最新心跳包时间，计算当前时间与heartState的差值，如果差值大于10秒钟，则认为设备离线 -->
      <el-table-column prop="heartState" header-align="center" align="center" width="100" label="在线状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.heartState && getTimeDiff(scope.row.heartState) <= 10" type="success">在线</el-tag>
          <el-tag v-else type="danger">离线</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="updateState" header-align="center" align="center" width="100" label="更新状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.updateState === 0">未更新</el-tag>
          <el-tag v-else-if="scope.row.updateState === 1">正在验证</el-tag>
          <el-tag v-else-if="scope.row.updateState === 2">下载中</el-tag>
          <el-tag v-else-if="scope.row.updateState === 3">待安装</el-tag>
          <el-tag v-else-if="scope.row.updateState === 4">安装中</el-tag>
          <el-tag v-else-if="scope.row.updateState === 5" type="success">安装完成</el-tag>
          <el-tag v-else-if="scope.row.updateState === 6" type="info">用户取消</el-tag>
          <el-tag v-else-if="scope.row.updateState === 7" type="danger">下载失败</el-tag>
          <el-tag v-else-if="scope.row.updateState === 8" type="danger">安装失败</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="currAppVersion" header-align="center" align="center" width="150" label="当前版本">
      </el-table-column>
      <el-table-column prop="updateAppVersion" header-align="center" align="center" width="150" label="配置版本">
      </el-table-column>
      <!-- 操作：详情 -->
      <el-table-column fixed="right" header-align="center" align="center" width="120" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small"
            @click="configHandle(scope.row)">配置升级</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <operate @close="getDataList" ref="operate" />
    <update-log v-if="updateLogVisible" ref="updateLog" />
  </div>
</template>

<script>
  import Operate from './deviceupdate-operate'
  import UpdateLog from './deviceupdate-updatelog'
  export default {
    data () {
      return {
        dataForm: {
          hospitalId: '',
          deptId: '',
          deviceName: '',
          deviceCode: '',
          deviceType: '',
          currVersion: '',
          updateVersion: '',
          updateStatus: []
        },
        cabTypeMap: {
          1: { label: '高值柜', value: 1 }
        },
        updateStateMap: {
          0: { label: '未更新', value: 0 },
          1: { label: '正在验证', value: 1 },
          2: { label: '下载中', value: 2 },
          3: { label: '待安装', value: 3 },
          4: { label: '安装中', value: 4 },
          5: { label: '安装完成', value: 5 },
          6: { label: '用户取消', value: 6 },
          7: { label: '下载失败', value: 7 },
          8: { label: '安装失败', value: 8 }
        },
        hospitalList: [],
        regionList: [],
        dataList: [],
        multipleSelection: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        operateVisible: false,
        updateLogVisible: false
      }
    },
    activated () {
      this.getDataList()
    },
    components: {
      Operate,
      UpdateLog
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          // TODO 获取配置设备列表
          url: this.$http.adornUrl('/ota/device/list'),
          method: 'post',
          data: this.$http.adornData({
            page: this.pageIndex,
            limit: this.pageSize,
            hospitalId: this.dataForm.hospitalId + '',
            deptId: this.dataForm.deptId + '',
            deviceName: this.dataForm.deviceName,
            deviceCode: this.dataForm.deviceCode,
            deviceType: this.dataForm.deviceType,
            currVersion: this.dataForm.currVersion,
            updateVersion: this.dataForm.updateVersion,
            updateStatus: this.dataForm.updateStatus
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              if (data.page.list.length === 0) {
                this.pageIndex = 1
                if (data.page.totalCount > 0) {
                  this.getDataList()
                } else {
                  this.dataList = []
                  this.totalPage = 0
                }
              } else {
                this.dataList = data.page.list
                this.totalPage = data.page.totalCount
              }
            } else {
              this.dataList = []
              this.totalPage = 0
            }
          })
          .finally(() => {
            this.dataListLoading = false
          })
      },
      // 获取医院列表
      getHospitalList () {
        this.$http({
          url: this.$http.adornUrl('/basic/hospital/info'),
          method: 'post',
          data: this.$http.adornData({
            page: 1,
            limit: -1
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.hospitalList = data.data.list
          } else {
            this.hospitalList = []
          }
        })
      },
      // 根据医院获取科室列表
      getRegionList () {
        this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({
            hospitalId: this.dataForm.hospitalId,
            page: 1,
            limit: -1
          })
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.regionList = data.data.list
            } else {
              this.$message.error(data.msg)
            }
          })
          .catch(() => {
            this.$message.error('网络连接异常，请稍后重试！')
          })
      },
      handleSelectionChange (val) {
        this.multipleSelection = val
      },
      search () {
        this.pageIndex = 1
        this.getDataList()
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      configHandle (detailData) {
        this.operateVisible = true
        this.$nextTick(() => {
          this.$refs.operate.init([detailData])
        })
      },
      updateMore () {
        if (this.multipleSelection.length === 0) return
        const firstDeviceType = this.multipleSelection[0].deviceType
        for (let i = 1; i < this.multipleSelection.length; i++) {
          // 如果发现任何一个设备的 deviceType 与第一个设备的不一致，返回 false
          if (this.multipleSelection[i].deviceType !== firstDeviceType) {
            return this.$message.error('请选择相同类型的设备！')
          }
        }
        this.operateVisible = true
        this.$nextTick(() => {
          this.$refs.operate.init(this.multipleSelection)
        })
      },
      updateLogShow () {
        this.updateLogVisible = true
        this.$nextTick(() => {
          this.$refs.updateLog.init()
        })
      },
      // 医院下拉框选择
      hospitalHandleChange () {
        this.dataForm.regionId = ''
      },
      // 计算时间差值（秒）
      getTimeDiff (heartState) {
        if (!heartState) return 999
        // 将时间字符串转换为时间戳
        const heartTime = new Date(heartState.replace(/-/g, '/')).getTime()
        const nowTime = new Date().getTime()
        // 计算时间差（秒）
        return Math.floor((nowTime - heartTime) / 1000)
      }
    }
  }
</script>