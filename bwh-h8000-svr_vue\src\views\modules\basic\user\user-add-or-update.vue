<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="80px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item label="工号" prop="jobNo">
        <el-input v-model="dataForm.jobNo" placeholder="请输入工号"></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-radio-group v-model="dataForm.sex">
          <el-radio :label="0">女</el-radio>
          <el-radio :label="1">男</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="所属科室" prop="deptId">
        <el-select filterable v-model="dataForm.deptId" placeholder="请选择科室" clearable @click.native="getDeptList">
          <el-option v-for="item in deptList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户等级" prop="level">
        <el-radio-group v-model="dataForm.level">
          <el-radio :label="0">普通用户</el-radio>
          <el-radio :label="1">管理员</el-radio>
          <el-radio :label="2">超级管理员</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input-number v-model="dataForm.age" :min="0" :max="150"></el-input-number>
      </el-form-item>
      <el-form-item label="备注" prop="memo">
        <el-input type="textarea" v-model="dataForm.memo" placeholder="请输入备注"></el-input>
      </el-form-item>
      <el-form-item label="电话" prop="tel">
        <el-input v-model="dataForm.tel" placeholder="请输入电话"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input type="password" v-model="dataForm.password" placeholder="请输入密码"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="isUse">
        <el-radio-group v-model="dataForm.isUse">
          <el-radio :label="0">停用</el-radio>
          <el-radio :label="1">启用</el-radio>
        </el-radio-group>
        <span style="color:#999;margin-left:10px;">当前状态: {{dataForm.isUse === 0 ? '停用' : '启用'}}</span>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          name: '',
          jobNo: '',
          sex: 0,
          deptId: '',
          deptCode: '',
          deptName: '',
          level: 0,
          age: 0,
          memo: '',
          tel: '',
          password: '',
          isUse: 1,
          hospitalId: '',
          sysRoleId: 0
        },
        deptList: [],
        dataRule: {
          name: [
            { required: true, message: '姓名不能为空', trigger: 'blur' }
          ],
          jobNo: [
            { required: true, message: '工号不能为空', trigger: 'blur' }
          ],
          deptId: [
            { required: true, message: '所属科室不能为空', trigger: 'change' }
          ],
          sex: [
            { required: true, message: '请选择性别', trigger: 'change' }
          ],
          level: [
            { required: true, message: '请选择用户等级', trigger: 'change' }
          ],
          password: [
            { required: true, message: '密码不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (row) {
        console.log('初始化窗口', row)
        // 先加载科室列表数据，无论是新增还是编辑
        this.getDeptList().then(() => {
          this.visible = true
          this.$nextTick(() => {
            // 保存原始值 (如果是编辑模式)
            const originalDeptCode = row ? row.deptCode : null
            const originalIsUse = row ? row.isUse : 1
            console.log('原始科室Code值:', originalDeptCode)
            console.log('原始用户状态:', originalIsUse, '类型:', typeof originalIsUse)
            // 重置表单 - 这会清空所有字段
            this.$refs['dataForm'].resetFields()
            if (row) {
              // 编辑模式 - 设置表单数据
              this.dataForm = {
                ...this.dataForm,
                ...row
              }
              // 确保isUse字段值为数字类型
              this.dataForm.isUse = parseInt(originalIsUse)
              console.log('设置后的用户状态:', this.dataForm.isUse)
              // 根据deptCode寻找匹配的科室
              this.$nextTick(() => {
                // 再次确认isUse设置正确
                this.dataForm.isUse = parseInt(originalIsUse)
                if (originalDeptCode) {
                  // 查找匹配的科室 (通过deptCode匹配)
                  const foundDept = this.deptList.find(d =>
                    d.code === originalDeptCode || String(d.code) === String(originalDeptCode)
                  )
                  if (foundDept) {
                    console.log('找到匹配科室:', foundDept)
                    this.dataForm.deptId = foundDept.id
                    // 确保deptCode和deptName也被更新
                    this.dataForm.deptCode = foundDept.code
                    this.dataForm.deptName = foundDept.name
                  } else {
                    console.log('未找到匹配科室，无法自动选择')
                  }
                  console.log('最终设置科室ID:', this.dataForm.deptId)
                }
              })
            } else {
              // 新增模式
              this.dataForm = {
                id: 0,
                name: '',
                jobNo: '',
                sex: 0,
                deptId: '',
                deptCode: '',
                deptName: '',
                level: 0,
                age: 0,
                memo: '',
                tel: '',
                password: '',
                isUse: 1,
                hospitalId: '',
                sysRoleId: 0
              }
            }
          })
        })
      },
      // 获取科室列表
      getDeptList () {
        return this.$http({
          url: this.$http.adornUrl('/basic/dept/info'),
          method: 'post',
          data: this.$http.adornData({})
        }).then(({ data }) => {
          if (data && data.code === 0 && data.data && data.data.list) {
            this.deptList = data.data.list
            // 检查列表中第一项的数据结构
            if (this.deptList.length > 0) {
              const firstDept = this.deptList[0]
              console.log('科室数据样例:', firstDept)
              console.log('科室ID类型:', typeof firstDept.id)
              console.log('科室Code示例:', firstDept.code)
              // 确保所有科室都有正确的ID和code属性
              this.deptList = this.deptList.map(dept => ({
                ...dept,
                id: typeof dept.id === 'string' ? Number(dept.id) : dept.id,
                code: dept.code || ''
              }))
            }
            console.log('科室列表加载成功:', this.deptList)
          } else {
            this.deptList = []
            console.warn('科室列表加载失败或为空')
          }
          return Promise.resolve()
        }).catch(error => {
          console.error('科室列表请求失败:', error)
          return Promise.reject(error)
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            // 确保科室ID是正确类型
            const formData = {
              ...this.dataForm
            }
            // 确保isUse字段值为数字类型
            console.log('提交前的用户状态:', formData.isUse, '类型:', typeof formData.isUse)
            formData.isUse = parseInt(formData.isUse)
            if (formData.deptId !== '' && formData.deptId !== null && formData.deptId !== undefined) {
              console.log('提交前的科室ID:', formData.deptId, '类型:', typeof formData.deptId)
              const selectedDept = this.deptList.find(item => {
                // 灵活匹配，处理数字和字符串类型
                return item.id === formData.deptId ||
                       String(item.id) === String(formData.deptId)
              })
              if (selectedDept) {
                console.log('找到匹配的科室:', selectedDept)
                formData.deptName = selectedDept.name
                formData.deptCode = selectedDept.code
              } else {
                // 如果通过ID没找到匹配科室，但已有deptCode，则保留现有值
                if (!formData.deptCode) {
                  console.error('未找到匹配的科室信息！deptId:', formData.deptId)
                  this.$message.error('未找到对应的科室信息，请重新选择科室')
                  return
                }
              }
            } else {
              this.$message.error('请选择科室')
              return
            }
            this.$http({
              url: this.$http.adornUrl(`/basic/user/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(formData)
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            }).catch(err => {
              console.error('提交表单时出错:', err)
            })
          }
        })
      }
    }
  }
</script>