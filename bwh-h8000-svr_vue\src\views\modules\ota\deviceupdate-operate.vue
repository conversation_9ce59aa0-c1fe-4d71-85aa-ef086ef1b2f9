<template>
  <el-dialog
    title="升级配置"
    :visible.sync="visible"
    center
    @close="handleClose"
  >
    <h4>升级设备：{{ getNames }}</h4>
    <el-form
      :model="dataForm"
      :rules="dataRule"
      inline
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit"
      label-width="80px"
    >
      <el-form-item label="升级软件" prop="appInfoId">
        <el-select
          filterable
          @change="handleChange"
          v-model="dataForm.appInfoId"
          placeholder="请选择升级软件"
        >
          <el-option
            v-for="item in appInfoList"
            :label="item.name"
            :key="item.index"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="软件版本" prop="updateVersionId">
        <el-select
          filterable
          :disabled="!dataForm.appInfoId"
          v-model="dataForm.updateVersionId"
          placeholder="请选择软件版本"
          @click.native="getVersionList"
        >
          <el-option
            v-for="item in versionList"
            :label="item.name"
            :key="item.index"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit">下发</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      appInfoList: [],
      versionList: [],
      devices: [],
      dataForm: {
        appInfoId: '',
        updateVersionId: ''
      },
      dataRule: {
        appInfoId: [
          { required: true, message: '升级软件不能为空', trigger: 'blur' }
        ],
        updateVersionId: [
          { required: true, message: '软件版本不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    getNames () {
      return this.devices.map(v => v.deviceName).join(',')
    }
  },
  directives: {
    loadMore: {
      bind (el, binding) {
        const selectDom = el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
        selectDom.addEventListener('scroll', function () {
          const height =
            selectDom.scrollHeight - selectDom.scrollTop <=
            selectDom.clientHeight
          if (height) {
            binding.value()
          }
        })
      }
    }
  },
  methods: {
    init (devices) {
      if (devices.length) {
        this.devices = devices
        this.visible = true
        this.$nextTick(() => {
          this.$http({
            // TODO 获取应用列表url
            url: this.$http.adornUrl(`/ota/appinfo/getAppSelect`),
            method: 'post'
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.appInfoList = data.list
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    },
    getVersionList () {
      this.$http({
        // TODO 获取应用版本url
        url: this.$http.adornUrl('/ota/appinfo/getAppVersionSelect'),
        method: 'post',
        params: this.$http.adornParams({
          appInfoId: this.dataForm.appInfoId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.versionList = data.list
        } else {
          this.versionList = []
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            // TODO 新增升级配置信息url
            url: this.$http.adornUrl('/ota/device/setInfo'),
            method: 'post',
            data: this.$http.adornData({
              updateVersionId: this.dataForm.updateVersionId,
              deviceIds: this.devices.map(v => v.id)
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    handleChange () {
      this.dataForm.updateVersionId = ''
    },
    handleClose () {
      this.$emit('close')
    }
  }
}
</script>

<style>
.el-descriptions-label {
  width: 200px;
  background-color: #e6e2e2;
  color: #909399;
  padding-left: 20px;
}
</style>
